"""Base tool class for agent tools."""

import logging
from typing import Any, Callable, Dict, List, Sequence, Tuple

from src.agent.config import AgentConfig
from src.tools.llm_client import LLMClient

logger = logging.getLogger(__name__)


class Tool:
    """Base class for all tools."""

    def __init__(
        self,
        name: str,
        description: str,
        func: Callable,
        arguments: List[Tuple[str, str]],
        outputs: Sequence,
        config: Dict[str, Any] = None,
    ):
        """Initialize a tool with its metadata."""
        self.name = name
        self.description = description
        self.func = func
        self.arguments = arguments
        self.outputs = outputs
        self.config = AgentConfig.from_dict(config or {})

        # Initialize LLM client if needed
        self.llm = LLMClient(
            model_name=self.config.model_name,
        )

    def to_string(self) -> str:
        """Return a string representation of the tool."""
        return (
            f"Tool Name: {self.name}\n"
            f"Description: {self.description}\n"
            f"Arguments: {', '.join(f'{arg[0]}: {arg[1]}' for arg in self._arguments)}\n"
            f"Outputs: {self.outputs}\n"
            f"Tool Name: {self.name}\n"
            f"Description: {self.description}\n"
            f"Arguments: {', '.join(f'{arg[0]}: {arg[1]}' for arg in self._arguments)}\n"
            f"Outputs: {self.outputs}\n"
        )

    def __call__(self, *args, **kwargs):
        """Invoke the underlying function with provided arguments."""
        return self.func(*args, **kwargs)
