"""Pipeline management module for the research agent."""

from typing import Dict, Any, List, Type
from src.pipelines.base import Pipeline
from src.pipelines.descriptive import DescriptivePipeline
from src.pipelines.impact import ImpactPipeline
from src.pipelines.comparative import ComparativePipeline
from src.pipelines.generalizability import GeneralizabilityPipeline
from src.pipelines.implementation import ImplementationPipeline
from src.pipelines.change import ChangePipeline
from src.pipelines.methodology import MethodologyPipeline
from src.tools.manager import ToolManager
from src.agent.utils import PipelineTracker
import logging

logger = logging.getLogger(__name__)


class PipelineManager:
    """Manages registration and access to agent pipelines."""

    def __init__(self, tool_manager: ToolManager, config: Dict[str, Any], session=None):
        """Initialize the pipeline manager with configuration.

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.pipelines: Dict[str, Pipeline] = {}
        self.intent_mapping: Dict[str, str] = {}
        self.session = session
        self.verbose = config.get("verbose", False)
        self.tool_manager = tool_manager

        # Initialize tracker
        conversation_id = config.get("conversation_id", "default")
        self.tracker = PipelineTracker(conversation_id)

        # Initialize pipelines
        self._initialize_pipelines()

    def _initialize_pipelines(self) -> None:
        """Initialize all available pipelines."""
        # Register default pipelines
        default_pipelines = [
            DescriptivePipeline,
            ImpactPipeline,
            ComparativePipeline,
            GeneralizabilityPipeline,
            ImplementationPipeline,
            ChangePipeline,
            MethodologyPipeline,
        ]

        for pipeline_class in default_pipelines:
            self._register_pipeline_class(pipeline_class)

    def _register_pipeline_class(self, pipeline_class: Type[Pipeline]) -> None:
        """Register a pipeline class.

        Args:
            pipeline_class: The pipeline class to register
        """
        pipeline_config = (
            dict(self.config) if isinstance(self.config, dict) else self.config.dict()
        )

        if self.session is not None:
            pipeline_config["session"] = self.session

        # Create and register only the specific pipeline class
        pipeline = pipeline_class(self.tool_manager, pipeline_config)

        # Set the tracker for the pipeline
        pipeline.set_tracker(self.tracker)

        self.register(pipeline)

        if self.verbose:
            logger.info(
                f"Registered pipeline: {pipeline.name} for intent: {pipeline.intent}"
            )

    def register(self, pipeline: Pipeline) -> None:
        """Register a new pipeline.

        Args:
            pipeline: The pipeline instance to register
        """
        if pipeline.name in self.pipelines:
            if self.verbose:
                logger.warning(f"Overwriting existing pipeline: {pipeline.name}")

        self.pipelines[pipeline.name] = pipeline

        # Map intent to pipeline name
        if pipeline.intent:
            self.intent_mapping[pipeline.intent] = pipeline.name

    def get_pipeline(self, name: str) -> Pipeline:
        """Get a registered pipeline by name.

        Args:
            name: The name of the pipeline to retrieve

        Returns:
            The requested pipeline instance

        Raises:
            ValueError: If the pipeline is not found
        """
        if name not in self.pipelines:
            available_pipelines = list(self.pipelines.keys())
            raise ValueError(
                f"Pipeline not found: {name}. Available pipelines: {available_pipelines}"
            )
        return self.pipelines[name]

    def get_pipeline_by_intent(self, intent: str) -> Pipeline:
        """Get a pipeline by intent.

        Args:
            intent: The intent to look up

        Returns:
            The pipeline associated with the intent

        Raises:
            ValueError: If no pipeline is found for the intent
        """
        if intent not in self.intent_mapping:
            available_intents = list(self.intent_mapping.keys())
            raise ValueError(
                f"No pipeline found for intent: {intent}. Available intents: {available_intents}"
            )

        pipeline_name = self.intent_mapping[intent]
        return self.get_pipeline(pipeline_name)

    def list_pipelines(self) -> List[Dict[str, Any]]:
        """List all registered pipelines with their metadata.

        Returns:
            List of pipeline information dictionaries
        """
        return [pipeline.to_dict() for pipeline in self.pipelines.values()]

    def list_intents(self) -> List[str]:
        """List all available intents.

        Returns:
            List of intent strings
        """
        return list(self.intent_mapping.keys())

    async def execute_pipeline(
        self, intent: str, user_query: str, **kwargs
    ) -> Dict[str, Any]:
        """Execute a pipeline based on intent.

        Args:
            intent: The intent that determines which pipeline to run
            user_query: The user's query to process
            **kwargs: Additional parameters for pipeline execution

        Returns:
            Dictionary containing the pipeline execution results
        """
        try:
            # Get the appropriate pipeline
            pipeline = self.get_pipeline_by_intent(intent)

            # Reset pipeline state
            pipeline.reset()

            if self.verbose:
                logger.info(f"Executing pipeline {pipeline.name} for intent {intent}")
                logger.info(f"Pipeline steps: {' -> '.join(pipeline.get_steps())}")

            # Start tracking the pipeline
            self.tracker.start_pipeline(pipeline.name, intent, user_query)

            # Execute the pipeline
            result = await pipeline.execute(user_query, **kwargs)

            # End tracking with success
            self.tracker.end_pipeline(success=True, final_result=result)

            # Add pipeline metadata to result
            result["pipeline_info"] = {
                "name": pipeline.name,
                "intent": intent,
                "status": pipeline.get_status(),
                "steps_executed": pipeline.get_steps(),
            }

            if self.verbose:
                logger.info(f"Pipeline {pipeline.name} completed successfully")

            return result

        except Exception as e:
            # End tracking with failure
            if hasattr(self, "tracker"):
                self.tracker.end_pipeline(success=False, final_result={"error": str(e)})

            if self.verbose:
                logger.error(f"Pipeline execution failed for intent {intent}: {e}")
            raise

    def get_pipeline_status(self, name: str) -> Dict[str, Any]:
        """Get the status of a specific pipeline.

        Args:
            name: The name of the pipeline

        Returns:
            Pipeline status information
        """
        pipeline = self.get_pipeline(name)
        return pipeline.get_status()

    def clear_all_caches(self) -> None:
        """Clear caches for all pipelines and the underlying tool manager."""
        if self.tool_manager:
            self.tool_manager.clear_cache()

        # Reset all pipelines
        for pipeline in self.pipelines.values():
            pipeline.reset()

        if self.verbose:
            logger.info("All pipeline and tool caches cleared")

    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get pipeline execution metrics summary with full data."""
        return self.tracker.get_summary(include_full_data=True)
