import React, { useEffect, useState } from "react";
import {
  Grid,
  Tabs,
  Tab,
  Box,
  Card,
  Typography,
  IconButton,
} from "@mui/material";
import ForestPlot from "./Graph/ForestPlot";
import StudiesRegionPlot from "./Graph/StudiesRegionPlot";
import SectorPlot from "./Graph/SectorPlot";
import { DownloadLinks } from "../../../types/ConversationTypes";
import CloseIcon from "@mui/icons-material/Close";
  
import "./PlotSection.css";
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
  theme: any;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, theme, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      {...other}
    >
      {value === index && (
        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            borderRadius: "8px",
            border: `1px solid ${theme.elevation.outlined}`,
            backgroundColor: `${theme.palette.background.default}`,
            overflow: "hidden",
            width: "100%",
            maxWidth: "100%",
            [theme.breakpoints.down("sm")]: {
              padding: "8px",
            },
          }}
        >
          {" "}
          {children}
        </Box>
      )}
    </div>
  );
}
interface PlotSectionProps {
  informationId: string;
  plotDataInfo: any;
  selectedOutcome: string;
  selectedIntervention: string;
  theme: any;
  onClose: () => void;
  activePlotDetails: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any; } | null; // Added new prop
}

const PlotSection: React.FC<PlotSectionProps> = ({
  informationId,
  plotDataInfo,
  selectedOutcome,
  selectedIntervention,
  theme,
  onClose,
  activePlotDetails,
}) => {
  const [plotLoading, setPlotLoading] = useState(false);
  const [plotError, setPlotError] = useState<string | null>(null);
  const [plotDataLabel, setPlotDataLabel] = useState<string | "">("");
  const [plotData, setPlotData] = useState<any>({});
  const [plotSectionId, setPlotSectionId] = useState<string | "">("");
  const [downloadLinks, setDownloadLinks] = useState<DownloadLinks | null>(
    null
  );
  const [value, setValue] = useState(0);

  const handleChange = (event: React.SyntheticEvent, newValue: number) => {
    setValue(newValue);
  };
  console.log("PLOTDATA", plotData, plotDataInfo);

  const plotData2 = {
    ...plotData,
    flat_effect_sizes: plotData?.flat_effect_sizes?.map((d) => ({
      ...d,
      all_sectors: Array.from(
        new Set(
          [[...d.outcome_sectors.split(";")],
          [...d.intervention_sectors.split(";")]].flat()
        )
      ),
      intervention_tag_short_labels: d.intervention_tag_short_labels.split(","),
      intervention_sectors: d.intervention_sectors?.split(";"),
      intervention_tag_ids: d.intervention_tag_ids.split(",").map((d) => +d),
      outcome_tag_short_labels: d.outcome_tag_short_labels
        .split(",")[0]
        ?.split(":")[0],
      outcome_sectors: d.outcome_sectors.split(";"),
      outcome_tag_ids: d.outcome_tag_ids.split(",").map((d) => +d),
      paper_id: +d.paper_id,
      quality_score_category: d.quality_score_category.replace(/Very /g, ""),
      income_group:
        d.income_group !== null
          ? d.income_group
            ?.replace(/ economies/g, " ")
            .replace(/-/g, " ")
            .trim()
          : "N/A",
    })),
    interventions: plotData?.interventions?.map((d) => ({
      ...d,
      intervention_id: +d.intervention_id,
      // intervention_label: d.intervention_label.split(":")[0],
      intervention_sectors: Array.from(
        new Set(d.intervention_sectors.split(",").map((d) => d.split(":")[0]))
      ),
      intervention_tag_ids: d.intervention_tag_ids.split(",").map((d) => +d),
    })),
    outcomes: plotData?.outcomes
      ?.map((d) => ({
        ...d,
        outcome_id: +d.outcome_id,
        // outcome_label: d.outcome_label.split(":")[0],
        outcome_tag_ids: d.outcome_tag_ids.split(",").map((d) => +d),
        outcome_sectors: Array.from(
          new Set(d.outcome_sectors.split(",").map((d) => d.split(":")[0]))
        ),
      }))
      .sort((a, b) =>
        a.outcome_tag_short_labels.localeCompare(b.outcome_tag_short_labels)
      ),
    studies: plotData?.studies?.map((d) => ({
      ...d,
      id: +d.id,
      quality_score_group: d.quality_score_group.replace(/Very /g, ""),
    })),
  };

  console.log("PLOTDATA2", plotData2);

  useEffect(() => {
    if (plotDataInfo) {
      const plotDataToSet = plotDataInfo.data?.data || plotDataInfo.data;
      if (plotDataToSet) {
        setPlotData(plotDataToSet);
      }
      if (plotDataInfo.download_links) {
        setDownloadLinks(plotDataInfo.download_links);
      }
      if (plotDataInfo.title) {
        setPlotDataLabel(plotDataInfo.title);
      }
    }
    if (informationId) {
      setPlotSectionId(informationId);
    }
  }, [plotDataInfo, informationId]);

  if (
    (!plotLoading &&
      (!plotData ||
        (Array.isArray(plotData)
          ? plotData.length === 0
          : Object.keys(plotData).length === 0))) ||
    plotError
  ) {
    return null;
  }

  return (
    <Card
      elevation={0}
      sx={{
        width: "100%",
        border: `1px solid ${theme.components.input.outlined.disabledBorder}`,
        borderRadius: "8px",
        display: "flex",
        flexDirection: "column",
        maxHeight: "100%",
        background: theme.common.white.main,
      }}
    >
      <Box
        sx={{
          overflowY: "auto",
          display: "flex",
          flexDirection: "column",
          height: "100%",
        }}
      >
        <Box
          sx={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            p: 2,
            pb: 3,
            position: "sticky",
            top: 0,
            background: theme.common.white.main,
            zIndex: 1,
          }}
        >
          <Typography
            variant="h6"
            component="div"
            sx={{ color: theme.palette.text.primary }}
          >
            Charts
          </Typography>
          <IconButton
            onClick={onClose}
            size="small"
            sx={{ color: theme.sidebar.secondaryFocused }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
        <Box sx={{ p: 2, pt: 0 }}>
          <Grid
            container
            spacing={0}
            mb={0}
            key={`plot-grid-${plotSectionId}`}
            id={`plot-grid-${plotSectionId}`}
            className="graph-sticky-element"
          >
            {plotData &&
              (Array.isArray(plotData)
                ? plotData.length > 0
                : Object.keys(plotData).length > 0) && (
                <Grid container spacing={2} mb={0}>
                  <Grid item xs={12} sm={12} md={12}>
                    <Tabs value={value} onChange={handleChange}>
                      <Tab
                        style={{
                          textTransform: "none",
                        }}
                        label="Impact"
                        value={0}
                      />
                      <Tab
                        style={{
                          textTransform: "none",
                        }}
                        label="Regions"
                        value={1}
                      />
                      <Tab
                        style={{
                          textTransform: "none",
                        }}
                        label="Sectors"
                        value={2}
                      />
                    </Tabs>
                    <div style={{ overflow: "scroll" }}>
                      <CustomTabPanel value={value} index={0} theme={theme}>
                        <ForestPlot
                          plotData={plotData2}
                          studiesData={plotData2?.studies}
                          plotLabel={plotDataLabel}
                          selectedOutcome={selectedOutcome}
                          selectedIntervention={selectedIntervention}
                          activePlotCitationIds={activePlotDetails?.citation_ids.map(item => item.value) || []}
                          activePlotMessageId={activePlotDetails?.messageId || null}
                        />
                      </CustomTabPanel>
                      <CustomTabPanel value={value} index={1} theme={theme}>
                        <StudiesRegionPlot plotData={plotData2} />
                      </CustomTabPanel>
                      <CustomTabPanel value={value} index={2} theme={theme}>
                        <SectorPlot plotData={plotData2} />
                      </CustomTabPanel>
                    </div>
                  </Grid>
                </Grid>
              )}
          </Grid>
        </Box>
      </Box>
    </Card>
  );
};
export default PlotSection;