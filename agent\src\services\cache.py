import os
import json
import uuid
import logging
from redis import ConnectionPool, Redis
from typing import Callable, Any, Optional, TypeVar

T = TypeVar("T")

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

host = os.getenv("REDIS_HOST", "redis")
port = int(os.getenv("REDIS_PORT", "6379"))

_connection_pool = None


def create_cache_key(prefix: str, text: str) -> str:
    hashed_text = uuid.uuid5(uuid.NAMESPACE_DNS, text)
    return f"{prefix}:{hashed_text}"


def initialize_redis_connection_pool():
    """Initialize the Redis connection pool."""
    global _connection_pool
    if _connection_pool is None:
        _connection_pool = ConnectionPool(host=host, port=port, decode_responses=True)
        logger.info(f"Redis connection pool created at {host}:{port}")


def destroy_redis_connection_pool():
    """Destroy the Redis connection pool."""
    global _connection_pool
    if _connection_pool:
        _connection_pool.disconnect()
        logger.info(f"Redis connection pool closed at {host}:{port}")


def get_redis_connection() -> Redis:
    """Return a Redis connection using the connection pool."""

    global _connection_pool
    if _connection_pool is None:
        initialize_redis_connection_pool()
    return Redis(connection_pool=_connection_pool)


def get_redis_pubsub():
    redis_client = get_redis_connection()
    return redis_client.pubsub()


class Cache:
    def __init__(self):
        """Initialize Cache class with Redis connection."""
        self.logger = logging.getLogger(__name__)
        self.redis = get_redis_connection()

    def has_key(self, key: str) -> bool:
        """Check if a key exists in the cache."""
        try:
            exists = self.redis.exists(key) > 0
            self.logger.debug(f"Key '{key}' exists: {exists}")
            return exists
        except Exception as e:
            self.logger.error(f"Failed to check key existence: {str(e)}")
            return False

    def get(self, key: str) -> Optional[int]:
        """Retrieve an integer value from the cache."""
        return self.redis.get(key)

    def set(self, key: str, value: int, expiration: int = 86400) -> None:
        """Store an integer value in the cache."""
        self.redis.set(key, value, ex=expiration)

    def get_json(self, key: str) -> Optional[Any]:
        """Retrieve and deserialize JSON data from the cache."""
        try:
            data = self.redis.get(key)
            if data is None:
                self.logger.debug(f"Key '{key}' not found in cache.")
                return None
            return json.loads(data)
        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to decode JSON for key '{key}': {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"Failed to get data for key '{key}': {str(e)}")
            return None

    def store_json(self, key: str, value: Any, expiration: int = 86400) -> bool:
        """Serialize and store JSON data in the cache."""
        try:
            data = json.dumps(value)
            self.redis.set(key, data, ex=expiration)
            self.logger.debug(
                f"Key '{key}' stored in cache with expiration {expiration} seconds."
            )
            return True
        except Exception as e:
            self.logger.error(f"Failed to store data for key '{key}': {str(e)}")
            return False

    def get_all_by_prefix(self, prefix: str) -> list:
        """Retrieve all keys with a given prefix."""
        try:
            keys = self.redis.keys(f"{prefix}*")
            self.logger.debug(f"Keys with prefix '{prefix}': {keys}")
            return keys
        except Exception as e:
            self.logger.error(f"Failed to get keys by prefix '{prefix}': {str(e)}")
            return []
