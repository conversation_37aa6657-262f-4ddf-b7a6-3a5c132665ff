import React from "react";
import { Paper, Box, Grid, Typography, Card, CardContent, CardMedia } from "@mui/material";
import { useTheme } from "@mui/material/styles";
import { useIsMobile, useIsTablet } from "../components/MobileUtils";

const NewsAndEventsFrame = () => {
    const isMobile = useIsMobile();
    const isTablet = useIsTablet();
    const isMobileOrTablet = isMobile || isTablet;

    const theme = useTheme();
    const features = [
        // {
        //     image: "/images/home/<USER>",
        //     caption: "December 2024",
        //     description: "<strong>New Way Now:</strong> How the World Bank provides crucial information to global policymakers in seconds",
        //     altText: "New Way Now - Leaders creating a new way forward with Google Cloud",
        //     url: "https://www.youtube.com/watch?v=VhUQI6Khfws",
        // },
        {
            image: "/images/home/<USER>",
            caption: "August 2025",
            description: "At the recent AFW LEADS Workshop in Togo, the AI Lab deepened collaborations with regional stakeholders, showcased our flagship product ImpactAI...",
            altText: "AWF LEADS",
            url: "https://dime-ai-test.my.canva.site/afw-leads-2025",
        },
        {
            image: "/images/home/<USER>",
            caption: "July 2025",
            description: "The Togo Data Lab is pioneering the use of AI and data tools to improve public services in Togo. At the 2025 LEADS Workshop, the Lab showcased...",
            altText: "Togo Data Lab",
            url: "https://dime-ai-test.my.canva.site/togo-data-lab-spotlight",
        },
        {
            image: "/images/home/<USER>",
            caption: "December 2024",
            description: "<strong>Apolitical's Government AI 100 list</strong> celebrates public servants from around the world leading on AI adoption, capacity-building, and...",
            altText: "Apolitical's Government AI 100 2025 List",
            url: "https://apolitical.co/list/en/government-ai-100-2025#AIcapacityandcapabilitybuilding",
        },
        {
            image: "/images/home/<USER>",
            caption: "December 2024",
            description: "<strong>AI for Social Good:</strong> Google's webinar on December 2, 2024, covered how the World Bank and other nonprofits are leveraging generative AI...",
            altText: "Google webinar on AI for social good",
            url: "https://cloudonair.withgoogle.com/events/ai-for-social-good-webinar",
        },
        // {
        //     image: "/images/home/<USER>",
        //     caption: "December 2024",
        //     description: "<strong>Exploring Generative AI for Development Impact:</strong> Learnings from a high-level panel, hosted by DIME, supported by Google.org",
        //     altText: "Panel discussion about Generative AI for development impact",
        //     url: "https://worldbank-dime-ai.my.canva.site/exploring-generative-ai-for-development-impact",
        // },
    ];

    const formatDescription = (description: string) => {
        const parts = [];
        let remaining = description;
        let index = 0;

        while (remaining) {
            const boldStart = remaining.indexOf("<strong>");
            const boldEnd = remaining.indexOf("</strong>");

            if (boldStart === -1 || boldEnd === -1) {
                parts.push(<span key={`part-${index}`}>{remaining}</span>);
                break;
            }
            if (boldStart > 0) {
                parts.push(<span key={`part-${index}`}>{remaining.substring(0, boldStart)}</span>);
            }

            parts.push(<strong key={`part-${index}`}>{remaining.substring(boldStart + 8, boldEnd)}</strong>);
            remaining = remaining.substring(boldEnd + 9);
            index++;
        }
        return parts;
    };
    return (
        <Paper
            sx={{
                padding: "0px",
                width: "100%",
                height: "auto",
                boxShadow: 0,
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                gap: "70px",
                borderRadius: "var(--4, 32px)",
                background: theme.palette.background.default,
            }}
        >
            {/* Header Section */}
            <Box
                sx={{
                    textAlign: "center",
                    display: "flex",
                    justifyContent: "center",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: 1
                }}>
                <Typography variant={isMobile ? "h3" : isTablet ? "h2" : "h2"}>
                    News & Events
                </Typography>
                <Typography variant="body1" sx={{ width: isMobile ? "95%" : "65%", textAlign: "center", color: theme.palette.text.primary }}>
                    Stay informed on official announcements, media coverage, and public engagements.
                </Typography>
            </Box>
            {/* Main Image Section */}
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    width: "100%",
                }}
            >
                <Grid container spacing={2} justifyContent="center">
                    {features.map((feature, index) => (
                        <Grid item xs={12} sm={3} md={3} key={index}>
                            <a href={feature.url} style={{ textDecoration: 'none' }} target="_blank" rel="noopener noreferrer" >
                                <Card
                                    sx={{
                                        display: "flex",
                                        flexDirection: "column",
                                        alignItems: "stretch",
                                        borderRadius: "24px",
                                        p: isMobileOrTablet ? '12px' : '20px',
                                        boxShadow: 0,
                                        height: "100%",
                                        background: "rgba(242, 246, 252, 1)", //F2F6FC
                                    }}
                                >
                                    <CardMedia
                                        component="img"
                                        image={feature.image}
                                        alt={feature.altText}
                                        sx={{
                                            flex: 1,
                                            width: "100%",
                                            borderRadius: "12px",
                                            objectFit: "cover",
                                        }}
                                    />

                                    <CardContent
                                        sx={{
                                            flex: 1,
                                            display: "flex",
                                            flexDirection: "column",
                                            justifyContent: "space-between",
                                            alignItems: "flex-start",
                                            gap: 2,
                                            px: 0,
                                            pb: '0px !important'
                                        }}
                                    >
                                        <Typography
                                            variant="body1"
                                            sx={{ fontSize: isMobile ? "12px" : "16px" }}
                                        >
                                            {formatDescription(feature.description)}
                                        </Typography>
                                        <Typography
                                            variant="body1"
                                            sx={{ color: theme.palette.text.secondary, fontSize: isMobile ? "12px" : "16px" }}
                                        >
                                            {feature.caption}
                                        </Typography>
                                    </CardContent>
                                </Card>
                            </a>
                        </Grid>
                    ))}
                </Grid>
            </Box>
        </Paper>
    );
};

export default NewsAndEventsFrame;