"""Methodology pipeline for explaining the agent's methodology and approach."""

from typing import Dict, Any, List
from src.pipelines.base import Pipeline
from src.tools.manager import ToolManager
from src.pipelines.error_handling import PipelineErrorHandler
from src.pipelines.outputs import create_pipeline_response
import logging

logger = logging.getLogger(__name__)


class MethodologyPipeline(Pipeline):
    """Pipeline for methodology queries about how ImpactAI collects, processes and analyzes data and calculates mean effects for interventions and outcomes."""

    def __init__(self, tool_manager: ToolManager, config: Dict[str, Any] = None):
        """Initialize the methodology pipeline."""
        super().__init__(
            name="methodology_pipeline",
            description="Pipeline for methodology queries about how ImpactAI collects, processes and analyzes data and calculates mean effects for interventions and outcomes",
            intent="methodology_inquiry",
            steps=["methodology_explainer"],
            arguments=[("user_query", "str")],
            outputs="MethodologyExplanation(text: str)",
            config=config,
        )
        logger.info("Initializing MethodologyPipeline")
        self.tool_manager = tool_manager
        self.error_handler = PipelineErrorHandler()

    def get_steps(self) -> List[str]:
        """Get the steps for methodology explanation."""
        return self.steps

    async def execute(self, user_query: str, **kwargs) -> Dict[str, Any]:
        """Execute the methodology pipeline."""
        try:
            logger.info(
                f"Starting methodology pipeline execution for query: {user_query}"
            )

            # Step 1: Generate Methodology Explanation
            logger.info("Step 1: Generating methodology explanation")
            explanation = await self._execute_step(
                "methodology_explainer", query=user_query
            )
            logger.info("Successfully generated methodology explanation")

            response = create_pipeline_response(
                intent="methodology_inquiry",
                status="completed_successfully",
                user_query=user_query,
                observation=explanation.text,
                pipeline_name=self.name,
                thought="Successfully generated methodology explanation based on the documentation.",
                steps_completed=self.get_steps(),
            )
            logger.info("Pipeline execution completed successfully")
            return response

        except Exception as e:
            logger.error(f"Pipeline error in {self.name}: {str(e)}")
            return self.error_handler.handle_pipeline_error(
                "methodology_inquiry", e, user_query, self.name
            )

    async def _execute_step(self, step_name: str, **kwargs):
        """Execute a pipeline step with error handling and tracking."""
        try:
            self.current_step += 1
            logger.info(f"Executing step {self.current_step}: {step_name}")

            # Start tracking the step
            self._track_step_start(step_name, kwargs)

            result = await self.tool_manager.execute_with_cache(step_name, **kwargs)
            self.results[step_name] = result

            # Track successful completion
            output_summary = {"type": type(result).__name__}
            if hasattr(result, "text"):
                output_summary["text_length"] = len(result.text)

            self._track_step_end(success=True, output_data=output_summary)
            logger.info(f"Successfully completed step {step_name}")

            return result

        except Exception as e:
            logger.error(f"Error in step {step_name}: {str(e)}")

            # Track the error
            self._track_step_end(success=False, error_message=str(e))
            raise
