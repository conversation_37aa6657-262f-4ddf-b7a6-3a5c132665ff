import json
import uuid

import google.generativeai as genai
import structlog
from models.agent import AgentResponse
from utils.measure import measure_async_time
from utils.requests import get_json, post_json
from utils.urls import get_agent_url
from services.files import FilesService

logger = structlog.get_logger(__name__)

files_service = FilesService()
agent_responses = files_service.load_agent_responses()

hashed_responses = {}
for response in agent_responses:
    key = response["response"]["context"]["query"]
    hashed_responses[key] = response


class AgentService:
    """Service for summarizing data based on query values."""

    def __init__(self, conversation_id: uuid.UUID, query: str):
        self.conversation_id = conversation_id
        self.query = query
        self.agent_api_url = get_agent_url()

    @measure_async_time
    async def execute(self) -> AgentResponse:
        """Fetches summary configs based on the given tag."""
        api_url = f"{self.agent_api_url}/execute"

        response_data = None
        if self.query in hashed_responses:
            response_data = hashed_responses[self.query]

        if not response_data:
            json_data = {
                "conversation_id": str(self.conversation_id),
                "query": self.query,
            }
            response_data = await post_json(api_url, body=json_data, timeout=300)
        try:
            tool_data = (
                response_data.get("response", {})
                .get("context", {})
                .get("tool_data", {})
            )
            if "data_url" in tool_data:
                try:
                    fetched_data_used = await get_json(tool_data["data_url"])
                    response_data["response"]["context"]["tool_data"][
                        "data_used"
                    ] = fetched_data_used
                except Exception as e:
                    logger.error("Failed to fetch data from data_url", error=e)
                    response_data["response"]["context"]["tool_data"]["data_used"] = []
            return AgentResponse.from_json(json.dumps(response_data["response"]))
        except Exception as e:
            logger.error("Failed to get agent response.", error=e)
            raise e


async def get_gemini_completion(
    prompt: str, model_name: str = "gemini-1.5-flash"
) -> str:
    """Generates a completion from a Gemini model with error handling."""
    try:
        model = genai.GenerativeModel(model_name)
        response = await model.generate_content_async(prompt)
        return response.text
    except Exception as e:
        logger.error("Gemini API call failed", error=e)
        return ""
