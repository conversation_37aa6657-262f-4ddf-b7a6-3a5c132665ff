import { useState, useEffect, useMemo, useContext } from "react";
import { get } from "../services/apiService";
import { API_ERROR_MESSAGE } from "../utils/labels";
import { ConversationResponse, Option, Message } from "../types/ConversationTypes";
import { AxiosError } from 'axios';
import { LayoutContext } from "../components/Layout/LayoutContext";

export const useConversationData = (
    conversationId: string,
    informationMessageId: string | null,
    localMessageList: Message[] | [],
    summaryStreamedText: string | null,
    refreshChatHistory: boolean,
    streamingError: boolean
) => {
    const [messages, setMessages] = useState<Message[]>([]);
    const [options, setOptions] = useState<Option[]>([]);
    const [errorLoadingMessages, setErrorLoadingMessages] = useState<string | null>(null);
    const [isLoadingMessages, setIsLoadingMessages] = useState<boolean>(false);
    const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);

    const { updateConversationId, updateAppLoaderState } = useContext(LayoutContext);

    useEffect(() => {
        const fetchConversationData = async () => {
            setIsLoadingMessages(true);
            setErrorLoadingMessages(null);
            try {
                updateAppLoaderState(true);
                setOptions([]);
                const response: ConversationResponse = await get(`/conversations/${conversationId}`);
                if (response.success) {
                    const newMessages = response.data.messages;
                    setMessages(newMessages);
                    setCurrentConversationId(conversationId);
                    if (isSingleSystemSuggestedTopic(newMessages)) {
                        setOptions(newMessages[0].choices!.options);
                    }
                } else {
                    setErrorLoadingMessages(response.error || API_ERROR_MESSAGE);
                }
            } catch (err: any) {
                setErrorLoadingMessages((err as AxiosError)?.message || err.message || API_ERROR_MESSAGE);
            } finally {
                setIsLoadingMessages(false);
                updateAppLoaderState(false);
            }
        };
        const shouldFetch = (
            !!conversationId && conversationId.length > 0 &&

            conversationId !== currentConversationId &&
            (
                refreshChatHistory && !informationMessageId ||
                (
                    !refreshChatHistory &&
                    !streamingError &&
                    !localMessageList[localMessageList.length - 1]?.id?.startsWith('system-loading') &&
                    !informationMessageId &&
                    !summaryStreamedText
                )
            )
        );
        if (shouldFetch) {
            updateConversationId(conversationId);
            fetchConversationData();
        } else if (!conversationId) {
            setMessages([]);
            setOptions([]);
            setCurrentConversationId(null);
            setIsLoadingMessages(false);
            updateAppLoaderState(false);
        } else if (conversationId && conversationId === currentConversationId) {
            setIsLoadingMessages(false);
            updateAppLoaderState(false);
        }
    }, [conversationId, informationMessageId]);

    const isSingleSystemSuggestedTopic = (messageArray: Message[]) => {
        return (
            messageArray.length === 1 &&
            messageArray[0].author === "system" &&
            messageArray[0].type === "question" &&
            messageArray[0].choices?.type === "suggested_topics"
        );
    };

    return {
        messages,
        options,
        errorLoadingMessages,
        isLoadingMessages,
        isSingleSystemSuggestedTopic: useMemo(() => isSingleSystemSuggestedTopic(messages), [messages]),
    };
};