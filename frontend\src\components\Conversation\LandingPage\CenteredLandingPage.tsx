import React, { useState, useEffect } from "react";
import { Box, Typography } from "@mui/material";
import BuildCircleIcon          from "../../Common/Icons/BuildCircleIcon";
import SupervisedUserCircleIcon from "../../Common/Icons/SupervisedUserCircleIcon";
import ConversationHeader from "../ConversationHeader";
import { TagEntry } from "../../../types/ConversationTypes";
import { useIsMobile, useIsTablet } from "../../Layout/MobileUtils";
import PromptSection from "../../PromptControl/PromptSection";
import { PROMPT_LABEL } from "../../../utils/labels";
import { useTheme } from "@mui/material/styles";
import { get, ApiResponse } from "../../../services/apiService";

interface CenteredLandingPageProps {
  handleUserPrompt: (message: string) => void;
}

enum tagTypes {
  Interventions = "interventions",
  Outcomes = "outcomes",
}

const tagList = [
  {
    key: 1,
    type: tagTypes.Interventions,
    label: "Interventions",
    placeholder: "Effectiveness of",
  },
  {
    key: 2,
    type: tagTypes.Outcomes,
    label: "Outcomes",
    placeholder: "Improvement of",
  },
];

type SearchChipsResponse = ApiResponse<{
  entries: {
    type: 'intervention' | 'outcome';
    value: string;
    label: string;
  }[];
}>;

const CenteredLandingPage: React.FC<CenteredLandingPageProps> = ({
  handleUserPrompt,
}) => {
  const isMobile = useIsMobile();
  const isTablet = useIsTablet();
  const isMobileOrTablet = isMobile || isTablet;
  const theme = useTheme();

  type TagTypesKeys = keyof typeof tagTypes;
  type TagTypesValues = (typeof tagTypes)[TagTypesKeys];

  const [dataTags, setDataTags] = useState<any>({
    [tagTypes.Interventions]: [],
    [tagTypes.Outcomes]: [],
  });
  const [selectedTag, setSelectedTag] = useState<TagTypesValues | null>(null);
  const [chipClickCount, setChipClickCount] = useState(0);

  useEffect(() => {
    const fetchData = async () => {
      const response = await get<SearchChipsResponse>(`/search/chips`);
      try {
        if (
          response.success &&
          response.data &&
          response.data.entries
        ) {
          const interventions: TagEntry[] = response.data.entries
            .filter((entry) => entry.type === "intervention")
            .map((entry) =>
            ({
              id: `intervention-chip-${entry.value}`,
              value: entry.value,
              label: entry.label,
            }))

          const outcomes: TagEntry[] = response.data.entries
            .filter((entry) => entry.type === "outcome")
            .map((entry) =>
            ({
              id: `outcome-chip-${entry.value}`,
              value: entry.value,
              label: entry.label,
            })
            );

          setDataTags({
            [tagTypes.Interventions]: interventions,
            [tagTypes.Outcomes]: outcomes,
          });
        } else {
          console.warn(
            "Unexpected response structure from /search/chips",
            response
          );
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      }
    };
    fetchData();
  }, []);

  const resetSelectedTag = () => {
    setSelectedTag(null);
  };

  const handleChipClick = (section: TagTypesValues) => {
    setSelectedTag(section || null);
    setChipClickCount((prevCount) => prevCount + 1);
    // No placeholder text change
  };

  const renderSectionContent = () => {
    return (
      <PromptSection
        centeredContent={true}
        handleChange={handleUserPrompt}
        queryLabelText={PROMPT_LABEL}
        isLoading={false}
        dataTags={dataTags}
        selectedTag={selectedTag}
        onCloseDropdown={resetSelectedTag}
        chipClickCount={chipClickCount}
      />
    );
  };

  return (
    <Box
      display="flex"
      flexDirection="column"
      mx="auto"
      className="chat-conversation"
      sx={{
        height: "calc(100vh - 160px)",
        overflowY: "hidden",
        transition: "padding-top 0.3s ease",
        width: isMobileOrTablet ? "100%" : "848px",
      }}
    >
      <Box
        className="chat-history-container"
        display="flex"
        flexDirection="column"
        flexGrow={1}
        px={0}
        height="100%"
        justifyContent="center"
        alignItems="center"
        position="relative"
      >
        <Box
          display="flex"
          justifyContent="center"
          alignItems="center"
          mx={isMobile ? "unset" : "auto"}
          pb={2}
        >
          <ConversationHeader isMobile={isMobile} />
        </Box>

        <Box width="100%" display="flex" justifyContent="center">
          {renderSectionContent()}
        </Box>

        <Box
          display="flex"
          justifyContent="center"
          gap={isMobileOrTablet ? 1 : 2}
          mt={isMobileOrTablet ? 1 : 0}
          sx={{
            flexDirection: { xs: "column", sm: "row" },
            alignItems: "center",
          }}
        >
          {tagList.map((tag) => {
            const Icon = tag.type === tagTypes.Interventions
              ? BuildCircleIcon
              : SupervisedUserCircleIcon;
            return (
              <Box
                key={tag.label}
                onClick={() => handleChipClick(tag.type)}
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  cursor: 'pointer',
                  bgcolor: theme.elevation.paperElevationTwo,
                  borderRadius: '100px',
                  px: 1.5,
                  py: 1,
                  gap: 0.75,
                  minWidth: 0,
                  transition: 'background 0.2s',
                  '&:hover, &:focus, &:active': {
                    backgroundColor: theme.palette.action.focus,
                  },
                  width: { xs: '100%', sm: 'auto' },
                  mb: { xs: 1, sm: 0 },
                }}
              >
                <Icon sx={{ fontSize: 24, color: theme.palette.text.secondary }} />
                <Typography
                  sx={{
                    fontWeight: 400,
                    color: theme.palette.text.primary,
                    fontSize: 14,
                    lineHeight: '18px',
                    letterSpacing: '0.16px',
                  }}
                >
                  {tag.label}
                </Typography>
              </Box>
            );
          })}
        </Box>
      </Box>
    </Box>
  );
};

export default CenteredLandingPage;