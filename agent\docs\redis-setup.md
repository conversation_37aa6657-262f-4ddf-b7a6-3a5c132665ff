# Redis Setup and Configuration Guide

## What is Redis?

Redis (Remote Dictionary Server) is an open-source, in-memory data structure store that can be used as a database, cache, message broker, and streaming engine. Think of it as a super-fast storage system that keeps data in your computer's memory (RAM) rather than on a disk, making data retrieval extremely quick.

### Why Use Redis?

**Performance Benefits:**
- **Lightning Fast**: Since data is stored in memory, Redis can perform millions of operations per second
- **Low Latency**: Data retrieval happens in microseconds instead of milliseconds
- **High Throughput**: Can handle massive amounts of concurrent requests

**Scalability Benefits:**
- **Distributed Caching**: Multiple application instances can share the same cache
- **Persistent Storage**: Data can survive application restarts
- **Cross-Instance Communication**: Perfect for distributed systems

## Why We Use Redis in This Project

### The Problem: Cloud Run and Serverless Scaling

Our AI agent application runs on **Google Cloud Run**, which is a serverless platform. Here's what this means and why it creates challenges:

#### What is Serverless?
- **No Fixed Servers**: Unlike traditional servers that run 24/7, serverless platforms like Cloud Run only run your application when there's a request
- **Automatic Scaling**: Cloud Run automatically creates new instances (copies) of your application when traffic increases
- **Cost Efficient**: You only pay for the time your code is actually running

#### The Scaling Challenge

Imagine this scenario:
1. **First Request** (10:00 AM): A researcher asks "What are poverty trends in Kenya?"
   - Cloud Run spins up **Instance A** to handle this request
   - The agent analyzes data and stores conversation history in memory
   - Response is sent back, Instance A goes idle

2. **Second Request** (10:30 AM): Same researcher asks "Can you elaborate on urban vs rural differences?"
   - Cloud Run might spin up **Instance B** (not Instance A) to handle this request
   - Instance B has NO memory of the previous conversation
   - The agent can't provide contextual responses because it lost all previous context

#### The Solution: Redis as a Shared Memory

By using Redis, we create a **shared memory system** that all Cloud Run instances can access:

```
┌─────────────┐    ┌─────────────────┐    ┌─────────────┐
│   Instance  │    │      Redis      │    │   Instance  │
│      A      │◄──►│   (Shared       │◄──►│      B      │
│             │    │    Memory)      │    │             │
└─────────────┘    └─────────────────┘    └─────────────┘
```

Now both instances can:
- Store conversation history in Redis
- Retrieve previous conversations from Redis
- Maintain context across requests
- Share analysis results and cached data

### Key Benefits for Our Use Case

1. **Persistent Conversations**: Researchers can have multi-session discussions spanning hours or days
2. **Contextual Responses**: The agent remembers previous analyses and builds upon them
3. **Improved Performance**: Cached analysis results speed up similar queries
4. **Reliability**: No data loss when Cloud Run scales up/down
5. **Team Collaboration**: Multiple researchers can build upon shared analysis contexts

## Installing Redis Locally

### Option 1: Using Homebrew (macOS)

```bash
# Install Redis using Homebrew
brew install redis

# Start Redis server
brew services start redis

# Verify Redis is running
redis-cli ping
# Should return "PONG"

# To stop Redis later
brew services stop redis
```

### Option 2: Using Docker (All Platforms)

```bash
# Pull and run Redis Docker container
docker run -d \
  --name redis-server \
  -p 6379:6379 \
  redis:7-alpine

# Verify Redis is running
docker exec redis-server redis-cli ping
# Should return "PONG"

# To stop Redis later
docker stop redis-server
docker rm redis-server
```

### Option 3: Using Package Managers

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install redis-server
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

**CentOS/RHEL:**
```bash
sudo yum install epel-release
sudo yum install redis
sudo systemctl start redis
sudo systemctl enable redis
```

**Windows:**
```bash
# Using WSL2 (recommended)
sudo apt update
sudo apt install redis-server
sudo service redis-server start

# Or download Windows binaries from:
# https://github.com/microsoftarchive/redis/releases
```

## Configuration

### Environment Variables

The agent uses these environment variables to configure Redis:

| Variable | Description | Default | Example |
|----------|-------------|---------|---------|
| `REDIS_CACHE_ENABLED` | Enable/disable Redis caching | `false` | `true` |
| `REDIS_HOST` | Redis server hostname | `localhost` | `localhost` |
| `REDIS_PORT` | Redis server port | `6379` | `6379` |
| `REDIS_PASSWORD` | Redis password (if required) | None | `your_password` |
| `REDIS_DB` | Redis database number | `0` | `0` |

### Setting Up Environment Variables

Create a `.env` file in your agent directory:

```bash
# Enable Redis caching
REDIS_CACHE_ENABLED=true

# Redis connection settings
REDIS_HOST=localhost
REDIS_PORT=6379

# Optional: If your Redis requires authentication
# REDIS_PASSWORD=your_password

# Optional: Use a specific database (0-15)
# REDIS_DB=0
```

Or export them directly:

```bash
export REDIS_CACHE_ENABLED=true
export REDIS_HOST=localhost
export REDIS_PORT=6379
```

## Testing Your Redis Setup

### 1. Basic Connection Test

```bash
# Test Redis connection directly
redis-cli ping
# Expected output: PONG

# Check Redis info
redis-cli info server
```

### 2. Test with the Agent

```bash
# Navigate to agent directory
cd agent

# Set up environment
export REDIS_CACHE_ENABLED=true
export REDIS_HOST=localhost
export REDIS_PORT=6379

# Test with a simple query
poetry run python -m src.agent.run_agent --query "Hello, test my Redis setup"

# Check if data is stored in Redis
redis-cli keys "*agent*"
# Should show keys like: "agent_history:your_conversation_id"
```

### 3. Test Conversation Persistence

```bash
# First query - note the conversation ID from logs
poetry run python -m src.agent.run_agent --query "What is poverty?"

# Extract conversation ID from the logs, then run:
# (Replace YOUR_CONVERSATION_ID with actual ID)
export CONVERSATION_ID="your_conversation_id_here"

# Second query using same conversation ID
poetry run python -m src.agent.run_agent \
  --conversation-id "$CONVERSATION_ID" \
  --query "Can you elaborate on rural poverty?"

# The agent should reference the previous conversation about poverty
```

## Monitoring Redis

### Using Redis CLI

```bash
# Monitor live commands
redis-cli monitor

# Check memory usage
redis-cli info memory

# List all keys
redis-cli keys "*"

# Get specific key data
redis-cli get "agent_history:your_conversation_id"
```

### Using Redis Desktop Tools

- **RedisInsight**: Official GUI tool from Redis
- **Redis Desktop Manager**: Third-party GUI
- **phpRedisAdmin**: Web-based interface

## Troubleshooting

### Common Issues

1. **Connection Refused**
   ```bash
   # Check if Redis is running
   ps aux | grep redis
   
   # Or check with systemctl (Linux)
   sudo systemctl status redis
   
   # Or with brew (macOS)
   brew services list | grep redis
   ```

2. **Permission Denied**
   ```bash
   # Check Redis configuration
   redis-cli config get dir
   redis-cli config get logfile
   
   # Fix permissions (Linux/macOS)
   sudo chown redis:redis /var/lib/redis
   ```

3. **Port Already in Use**
   ```bash
   # Check what's using port 6379
   lsof -i :6379
   
   # Kill the process if needed
   sudo kill -9 PID_NUMBER
   ```

4. **Agent Fallback to In-Memory**
   - Check environment variables are set correctly
   - Verify Redis is accessible: `redis-cli ping`
   - Check agent logs for Redis connection errors
   - Ensure Redis host/port match your configuration

### Verification Commands

```bash
# Test Redis connection from Python
python3 -c "
import redis
r = redis.Redis(host='localhost', port=6379, decode_responses=True)
print('Redis ping:', r.ping())
print('Redis info server:', r.info('server')['redis_version'])
"
```

## Security Considerations

### Production Setup

1. **Enable Authentication**
   ```bash
   # Add to redis.conf
   requirepass your_strong_password
   ```

2. **Bind to Specific Interface**
   ```bash
   # Add to redis.conf (don't bind to all interfaces in production)
   bind 127.0.0.1
   ```

3. **Disable Dangerous Commands**
   ```bash
   # Add to redis.conf
   rename-command FLUSHDB ""
   rename-command FLUSHALL ""
   rename-command DEBUG ""
   ```

4. **Use TLS/SSL** (for remote Redis)
   ```bash
   export REDIS_SSL=true
   export REDIS_SSL_CERT_PATH=/path/to/cert.pem
   ```

## Performance Tuning

### Memory Management

```bash
# Set memory limit
redis-cli config set maxmemory 256mb

# Set eviction policy
redis-cli config set maxmemory-policy allkeys-lru
```

### Persistence Settings

```bash
# Disable persistence for pure cache use
redis-cli config set save ""

# Or configure periodic saves
redis-cli config set save "900 1 300 10 60 10000"
```

## FAQ

**Q: Do I need Redis for development?**
A: No, the agent will work fine without Redis using in-memory storage. Redis is mainly beneficial for production deployments and when you need conversation persistence.

**Q: Will my conversations be lost if Redis goes down?**
A: The agent will automatically fall back to in-memory storage. You'll lose persistence but the application will continue working.

**Q: How much memory does Redis use?**
A: This depends on your usage. Start with 128-256MB for development. Monitor with `redis-cli info memory`.

**Q: Can multiple agents share the same Redis instance?**
A: Yes, Redis is designed for concurrent access. Each conversation gets a unique key.

**Q: How long are conversations stored?**
A: By default, indefinitely. You can set TTL (time-to-live) values if needed.