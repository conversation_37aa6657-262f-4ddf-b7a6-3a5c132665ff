"""SQL query generation module for development economics research."""

import json
import logging
import time
import uuid
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Tuple, Union

import aiohttp
import pandas as pd
import requests
from pydantic import BaseModel
from sqlalchemy import text

from src.agent.config import DatasetSettings
from src.tools.base import Tool
from src.tools.entity_extractor import EntityMention, ExtractedEntities
from src.utils.db import create_db_engine
from src.utils.file_management import DatasetManager

logger = logging.getLogger(__name__)

# API Configuration
SQL_API_BASE_URL = "https://text2sql-din4qs2qka-uc.a.run.app"
MAX_VALIDATION_ITERATIONS = 5


class QueryResult(BaseModel):
    """Result of a SQL query execution with separate paper metadata and analysis data."""

    user_query: str
    dataset: Optional[str] = None
    row_count: int
    unique_papers: int
    paper_ids: Dict[str, Any]
    dict_rows: List[Dict[str, Any]]
    execution_time: float
    metadata: Dict[str, Any] = {}

    def __str__(self) -> str:
        """String representation of the query result."""
        base_info = {
            "user_query": self.metadata.get("sql_query", {}).get(
                "user_query", self.user_query
            ),
            "dataset": self.dataset,
            "row_count": self.row_count,
            "unique_papers": self.unique_papers,
            "execution_time": self.execution_time,
        }

        input_rag = json.dumps(base_info, indent=4)

        input_structured_data = json.dumps(
            {
                **base_info,
                "entities": "ExtractedEntities(user_query: str, country_codes: List[str], "
                "delivery_strategies: List[EntityMention], interventions: List[EntityMention], "
                "outcomes: List[EntityMention], sectors: List[EntityMention], "
                "target_populations: List[EntityMention], extended_interventions: List[EntityMention], "
                "extended_outcomes: List[EntityMention], extended_intervention_sectors: List[EntityMention], "
                "extended_intervention_target_populations: List[EntityMention], "
                "extended_outcome_sectors: List[EntityMention], "
                "extended_outcome_target_populations: List[EntityMention])",
            },
            indent=4,
        )

        return (
            f"Query executed successfully in {self.execution_time:.2f}s.\n"
            f"Found {self.row_count} data points across {self.unique_papers} unique papers.\n"
            f"Paper ids and figures dataset saved at {self.dataset}\n"
            f"Input for the RAG Searcher (if necessary):\n{input_rag}\n"
            f"Input for the Structured Data Organizer (if necessary):\n{input_structured_data}"
        )

    def to_dataframe(self) -> pd.DataFrame:
        """Convert query data to a pandas DataFrame."""
        return pd.DataFrame(self.dict_rows)

    def to_dict(self) -> Dict[str, Any]:
        """Convert query result to a dictionary."""
        return self.dict()

    def get_paper_combined_ids(self) -> List[str]:
        """Get list of unique paper IDs."""
        return list(self.paper_ids.keys())


class SQLQuery(BaseModel):
    """Generated SQL query with metadata."""

    user_query: str
    sql_query: str
    reduced_scope: bool = False
    metadata: Dict[str, Any] = {}

    def __str__(self) -> str:
        """String representation of the query."""
        output = json.dumps(
            {
                "user_query": self.user_query,
                "sql_query": self.sql_query,
                "reduced_scope": self.reduced_scope,
            },
            indent=4,
        )
        scope_note = " (with reduced scope)" if self.reduced_scope else ""
        return (
            f"We have just generated the SQL query{scope_note}.\n"
            f"Original Query: {self.user_query}\n"
            f"Output: SQLQuery(user_query={self.user_query}, sql_query={self.sql_query})"
            f"Input for the SQL Executor:\n{output}"
        )


@dataclass
class Text2SQLPayload:
    """Data class for the Text2SQL API payload."""

    user_query: str
    country_codes: List[str]
    intervention_sectors: List[EntityMention]
    intervention_target_populations: List[EntityMention]
    outcome_sectors: List[EntityMention]
    outcome_target_populations: List[EntityMention]
    interventions: List[EntityMention]
    outcomes: List[EntityMention]

    def _entity_mentions_to_dict(
        self, entities: List[EntityMention]
    ) -> List[Dict[str, Any]]:
        """Convert EntityMention objects to dictionary format."""
        return [
            {
                "id": entity.id,
                "label": entity.label,
                "mention": entity.mention,
                "short_label": entity.short_label,
            }
            for entity in entities
        ]

    def to_dict(self) -> Dict[str, Any]:
        """Convert the payload to the API-expected dictionary format."""
        entity_fields = [
            "interventions",
            "outcomes",
            "intervention_sectors",
            "intervention_target_populations",
            "outcome_sectors",
            "outcome_target_populations",
        ]

        result = {
            "user_query": self.user_query,
            "country_codes": self.country_codes,
        }

        # Add all entity fields using the helper method
        for field in entity_fields:
            result[field] = self._entity_mentions_to_dict(getattr(self, field))

        return result


class Text2SQLClient:
    """Client for interacting with the Text2SQL service."""

    def __init__(self, base_url: str = SQL_API_BASE_URL, session=None):
        self.base_url = base_url.rstrip("/")
        self.generate_endpoint = f"{self.base_url}/generate-sql/"
        self.correct_endpoint = f"{self.base_url}/correct-sql/"
        self.session = session
        self.owns_session = False

    async def _get_session(self):
        """Get a valid session for API calls."""
        if self.session is not None and not self.session.closed:
            return self.session

        self.session = aiohttp.ClientSession()
        self.owns_session = True
        return self.session

    async def _make_request(
        self, endpoint: str, payload: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Make an HTTP POST request to the specified endpoint."""
        headers = {"Content-Type": "application/json"}
        try:
            session = await self._get_session()
            async with session.post(
                endpoint, json=payload, headers=headers
            ) as response:
                response.raise_for_status()
                return await response.json()
        except Exception as e:
            raise Exception(f"Error calling Text2SQL service: {str(e)}")

    async def generate_sql(self, payload: Text2SQLPayload) -> Dict[str, Any]:
        """Generate SQL from the payload."""
        return await self._make_request(self.generate_endpoint, payload.to_dict())

    async def correct_sql(
        self, user_query: str, sql_query: str, entities: Dict[str, Any], error: str
    ) -> Dict[str, Any]:
        """Correct SQL query using the correction endpoint."""
        payload = {
            "user_query": user_query,
            "sql_query": sql_query,
            "entities": entities,
            "error_message": error,
        }
        return await self._make_request(self.correct_endpoint, payload)

    async def cleanup(self):
        """Clean up resources."""
        if self.owns_session and self.session is not None and not self.session.closed:
            await self.session.close()
            self.session = None


class SQLGenerator(Tool):
    """Tool for generating SQL queries."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize the SQL generator."""
        super().__init__(
            name="sql_generator",
            description="Create SQL queries to find relevant research papers and data in research databases and run SQL queries against research databases to get metadata from research papers and/or quantitative results",
            func=self.generate,
            arguments=[
                (
                    "entities",
                    "ExtractedEntities(user_query: str, country_codes: List[str], interventions: List[EntityMention], outcomes: List[EntityMention], intervention_sectors: List[EntityMention], intervention_target_populations: List[EntityMention], outcome_sectors: List[EntityMention], outcome_target_populations: List[EntityMention])",
                )
            ],
            outputs=[
                (
                    "query_result",
                    "QueryResult(user_query: str, dataset: str, row_count: int, unique_papers: int, paper_ids: Dict[str, Any], execution_time: float, metadata: Dict[str, Any])",
                )
            ],
            config=config,
        )
        dataset_settings = DatasetSettings()
        self.engine = create_db_engine()
        self.verbose = config.get("verbose", False)
        self.dataset_manager = DatasetManager(dataset_settings)

        # Save the session reference but don't use it directly
        self.session = config.get("session")
        self.owns_session = False

        # Create the client with the session reference
        self.client = Text2SQLClient(session=self.session)

    def _extract_extended_entities(
        self, entities_dict: Dict[str, Any]
    ) -> Tuple[Dict[str, Any], str]:
        """Extract and format extended entities with user-friendly message."""
        extended_entities_dict = {
            key: entities_dict.get(key, [])[:3]
            for key in [
                "extended_interventions",
                "extended_outcomes",
                "extended_intervention_sectors",
                "extended_outcome_sectors",
                "extended_intervention_target_populations",
                "extended_outcome_target_populations",
            ]
        }

        # Format extended entities for user-friendly display
        formatted_entities = []
        entity_mappings = {
            "extended_interventions": "Interventions",
            "extended_outcomes": "Outcomes",
            "extended_intervention_sectors": "Intervention sectors",
            "extended_outcome_sectors": "Outcome sectors",
        }

        for key, label in entity_mappings.items():
            if extended_entities_dict[key]:
                labels = [e["label"] for e in extended_entities_dict[key]]
                formatted_entities.append(f"{label}: {', '.join(labels)}")

        message = (
            "No data found with current search terms. Consider using these extended search terms: "
            + ", ".join(formatted_entities)
        )

        return extended_entities_dict, message

    async def _validate_query(
        self, user_query: str, sql_query: str, entities: Dict[str, Any]
    ) -> Tuple[str, bool, str, Dict[str, Any], Optional[List[Any]]]:
        """Validate and potentially correct the SQL query.

        Returns:
            Tuple containing:
            - Validated/corrected SQL query
            - Boolean indicating if corrections were made
            - Additional message about data availability or suggestions
            - Dictionary with extended entities that could be used (if no data found)
            - Query results (if successful) or None
        """
        correction_history = []
        current_sql_query = sql_query

        # First try the original query
        try:
            with self.engine.connect() as connection:
                result = connection.execute(text(current_sql_query))
                rows = result.fetchall()

                # If we have data, return the original query with results
                if len(rows) > 0:
                    return (
                        current_sql_query,
                        False,
                        "Query executed successfully.",
                        {},
                        rows,
                    )

                # If no data, check for extended entities
                logger.info(
                    "No data found with original query, trying extended entities"
                )
                entities_dict = entities.to_dict()

                has_extended_entities = any(
                    entities_dict.get(key, [])
                    for key in [
                        "extended_interventions",
                        "extended_outcomes",
                        "extended_intervention_sectors",
                        "extended_outcome_sectors",
                        "extended_intervention_target_populations",
                        "extended_outcome_target_populations",
                    ]
                )

                if has_extended_entities:
                    extended_entities_dict, message = self._extract_extended_entities(
                        entities_dict
                    )
                    return (
                        current_sql_query,
                        False,
                        message,
                        extended_entities_dict,
                        rows,
                    )

                return (
                    current_sql_query,
                    False,
                    "No data found in the database matching your criteria, and no extended entities available.",
                    {},
                    rows,
                )

        except Exception as e:
            error_message = str(e)
            if self.verbose:
                logger.warning(f"SQL query validation failed: {error_message}")
                logger.info(f"Entities: {entities.to_dict()}")
                logger.info(f"Type Entities: {type(entities.to_dict())}")

            try:
                result = await self.client.correct_sql(
                    user_query, current_sql_query, entities.to_dict(), error_message
                )
                current_sql_query = result.get("sql_query", current_sql_query)
                correction_history.append({"iteration": 1, "error": error_message})

                if self.verbose:
                    logger.info(f"Corrected SQL query: {current_sql_query}")

            except requests.exceptions.RequestException as e:
                if self.verbose:
                    logger.error(f"Failed to correct SQL query: {e}")
                raise ValueError(f"SQL query correction failed. Error: {e}")

        error_summary = "\n".join(
            [f"Iteration {h['iteration']}: {h['error']}" for h in correction_history]
        )
        raise ValueError(
            f"Failed to validate SQL query.\nError history:\n{error_summary}"
        )

    def _convert_entity(
        self, entities: Union[Dict[str, Any], ExtractedEntities, str]
    ) -> ExtractedEntities:
        """Convert API response to ExtractedEntities object."""

        if isinstance(entities, ExtractedEntities):
            return entities

        if isinstance(entities, str):
            try:
                entities = json.loads(entities)
            except json.JSONDecodeError:
                raise ValueError(f"Failed to parse entities: {entities}")

        try:
            if "interventions" in entities and entities["interventions"]:
                _ = [
                    intervention.pop("closest_group", None)
                    for intervention in entities.get("interventions", [])
                ]
            if "outcomes" in entities and entities["outcomes"]:
                _ = [
                    outcome.pop("closest_group", None)
                    for outcome in entities.get("outcomes", [])
                ]
            # Convert snake_case API keys to kebab-case
            mapped_response = {
                "user_query": entities.get("user_query"),
                "country_codes": entities.get("country_codes", []),
                "interventions": [
                    EntityMention(**intervention)
                    for intervention in entities.get("interventions", [])
                ],
                "outcomes": [
                    EntityMention(**outcome) for outcome in entities.get("outcomes", [])
                ],
                "intervention_sectors": [
                    EntityMention(**sector)
                    for sector in entities.get("intervention_sectors", [])
                ],
                "intervention_target_populations": [
                    EntityMention(**population)
                    for population in entities.get(
                        "intervention_target_populations", []
                    )
                ],
                "outcome_sectors": [
                    EntityMention(**sector)
                    for sector in entities.get("outcome_sectors", [])
                ],
                "outcome_target_populations": [
                    EntityMention(**population)
                    for population in entities.get("outcome_target_populations", [])
                ],
            }
            return ExtractedEntities(**mapped_response)
        except Exception as e:
            if self.verbose:
                logger.error(f"Error converting API response: {e}")
            raise ValueError(f"Failed to convert API response: {e}")

    async def generate(
        self, entities: Union[ExtractedEntities, Dict[str, Any]]
    ) -> SQLQuery:
        """Generate SQL query from extracted entities."""
        try:
            entities = self._convert_entity(entities)
            payload = Text2SQLPayload(
                user_query=entities.user_query,
                country_codes=entities.country_codes,
                interventions=entities.interventions,
                outcomes=entities.outcomes,
                intervention_sectors=entities.intervention_sectors,
                intervention_target_populations=entities.intervention_target_populations,
                outcome_sectors=entities.outcome_sectors,
                outcome_target_populations=entities.outcome_target_populations,
            )

            # Generate SQL
            api_response = await self.client.generate_sql(payload)
            sql_query = api_response.get("sql_query", "")

            # Validate and potentially get results
            (
                validated_query,
                was_corrected,
                additional_message,
                extended_entities,
                cached_rows,
            ) = await self._validate_query(
                user_query=entities.user_query,
                sql_query=sql_query,
                entities=entities,
            )

            if self.verbose:
                logger.info(f"Final SQL query: {validated_query}")

            sql_query_obj = SQLQuery(
                user_query=entities.user_query,
                sql_query=validated_query,
                reduced_scope=False,
                metadata={
                    "entities": entities.dict(),
                    "was_corrected": was_corrected,
                    "message": (
                        additional_message
                        if additional_message
                        else "Query executed successfully."
                    ),
                    "extended_entities": extended_entities,
                },
            )

            # Use cached results if available, otherwise execute
            return await self.execute_sql_query(sql_query_obj, cached_rows)

        except Exception as e:
            if self.verbose:
                logger.error(f"Error generating SQL query: {str(e)}")
            return await self.execute_sql_query(
                SQLQuery(
                    user_query=entities.user_query,
                    sql_query="SELECT id, title, year, country FROM Papers WHERE year < 2020 LIMIT 10;",
                    reduced_scope=False,
                    metadata={"error": str(e)},
                )
            )

    def _pre_process_fix_temporal(
        self, dict_rows: List[Dict[str, Any]], entities: ExtractedEntities
    ) -> List[Dict[str, Any]]:
        """Pre-process rows to map entity labels, short_labels, and definitions from extracted entities."""
        if not dict_rows:
            return dict_rows

        # Create lookup dictionaries for all entity types
        entity_lookup = {}

        # Add all entity types to lookup
        for field_name in entities._ENTITY_FIELDS:
            field_entities = getattr(entities, field_name, [])
            for entity in field_entities:
                entity_id = str(entity.id)
                entity_lookup[entity_id] = entity

        def update_tags(
            ids_str: str, labels_str: str, short_labels_str: str, definitions_str: str
        ) -> tuple:
            """Update tag strings based on entity lookup."""
            if not ids_str:
                return labels_str, short_labels_str, definitions_str

            ids = ids_str.split(";")
            labels = labels_str.split(";") if labels_str else [""] * len(ids)
            short_labels = (
                short_labels_str.split(";") if short_labels_str else [""] * len(ids)
            )
            definitions = (
                definitions_str.split(";") if definitions_str else [""] * len(ids)
            )

            # Ensure all lists have the same length
            max_len = len(ids)
            labels = labels + [""] * (max_len - len(labels))
            short_labels = short_labels + [""] * (max_len - len(short_labels))
            definitions = definitions + [""] * (max_len - len(definitions))

            updated_labels = []
            updated_short_labels = []
            updated_definitions = []

            for i, tag_id in enumerate(ids):
                tag_id = tag_id.strip()
                if tag_id in entity_lookup:
                    entity = entity_lookup[tag_id]
                    updated_labels.append(entity.label if entity.label else labels[i])
                    updated_short_labels.append(
                        entity.short_label if entity.short_label else short_labels[i]
                    )
                    updated_definitions.append(
                        entity.definition if entity.definition else definitions[i]
                    )
                else:
                    # Keep original values if no match found
                    updated_labels.append(labels[i])
                    updated_short_labels.append(short_labels[i])
                    updated_definitions.append(definitions[i])

            return (
                ";".join(updated_labels),
                ";".join(updated_short_labels),
                ";".join(updated_definitions),
            )

        # Process each row
        processed_rows = []
        for row in dict_rows:
            new_row = row.copy()

            # Update intervention tags
            if "intervention_tag_ids" in row:
                updated_labels, updated_short_labels, updated_definitions = update_tags(
                    row.get("intervention_tag_ids", ""),
                    row.get("intervention_tag_labels", ""),
                    row.get("intervention_tag_short_labels", ""),
                    row.get("intervention_tag_definitions", ""),
                )
                new_row["intervention_tag_labels"] = updated_labels
                new_row["intervention_tag_short_labels"] = updated_short_labels
                new_row["intervention_tag_definitions"] = updated_definitions

            # Update outcome tags
            if "outcome_tag_ids" in row:
                updated_labels, updated_short_labels, updated_definitions = update_tags(
                    row.get("outcome_tag_ids", ""),
                    row.get("outcome_tag_labels", ""),
                    row.get("outcome_tag_short_labels", ""),
                    row.get(
                        "outcome_tag_definition", ""
                    ),  # Note: singular 'definition'
                )
                new_row["outcome_tag_labels"] = updated_labels
                new_row["outcome_tag_short_labels"] = updated_short_labels
                new_row["outcome_tag_definition"] = (
                    updated_definitions  # Note: singular 'definition'
                )

            processed_rows.append(new_row)

        return processed_rows

    async def execute_sql_query(
        self, sql_query: SQLQuery, cached_rows: Optional[List[Any]] = None
    ) -> QueryResult:
        start_time = time.time()

        if self.verbose:
            logger.info(f"Executing query: {sql_query.sql_query}")

        # Use cached results if available
        if cached_rows is not None:
            rows = cached_rows
            if self.verbose:
                logger.info(f"Using cached results: {len(rows)} rows")
        else:
            # Execute query if no cached results
            with self.engine.connect() as conn:
                stmt = text(sql_query.sql_query)
                result = conn.execute(stmt)
                rows = result.fetchall()

        if self.verbose:
            logger.info(f"Rows number: {len(rows)}")

        dict_rows = [
            {
                k: v
                for k, v in row._mapping.items()
                if k not in ["intervention_start_date", "intervention_end_date"]
            }
            for row in rows
        ]

        # Pre-process rows to fix temporal mapping with extracted entities
        entities = self._convert_entity(sql_query.metadata.get("entities", {}))
        dict_rows = self._pre_process_fix_temporal(dict_rows, entities)

        unique_id = str(uuid.uuid4())
        saved_blob_path = f"datasets/{unique_id}.json"
        dataset_uri = await self.dataset_manager.save_dataset(
            dict_rows, saved_blob_path
        )
        if self.verbose:
            logger.info(f"Dataset saved to GCP: {dataset_uri}")

        execution_time = time.time() - start_time

        paper_ids = {row["paper_combined_id"]: row["title"] for row in dict_rows}

        # Pass extended entities from sql_query.metadata if present
        extended_entities = sql_query.metadata.get("extended_entities", {})
        has_extended_entities = any(extended_entities.values())

        return QueryResult(
            user_query=sql_query.user_query,
            dataset=dataset_uri,
            paper_ids=paper_ids,
            row_count=len(dict_rows),
            dict_rows=dict_rows,
            unique_papers=len(paper_ids),
            execution_time=execution_time,
            metadata={
                "sql_query": sql_query.dict(),
                "has_extended_entities": has_extended_entities,
                "extended_entities": (
                    extended_entities if has_extended_entities else {}
                ),
                "message": sql_query.metadata.get("message", ""),
            },
        )

    async def cleanup(self):
        """Clean up resources used by the tool."""
        # Close any session or connection this tool might have
        if hasattr(self, "session") and self.session is not None:
            await self.session.close()
            self.session = None
