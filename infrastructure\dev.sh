#!/bin/bash

# Development startup script for causal-ai-product
# Usage: ./dev.sh [start|stop|install|help]

set -e

# Source the service map configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
source "$SCRIPT_DIR/service-map.sh"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check dependencies
check_dependencies() {
    print_status "Checking dependencies..."
    
    if ! command_exists poetry; then
        print_error "Poetry is not installed. Please install it first."
        exit 1
    fi
    
    if ! command_exists node; then
        print_error "Node.js is not installed. Please install it first."
        exit 1
    fi
    
    if ! command_exists npm; then
        print_error "npm is not installed. Please install it first."
        exit 1
    fi
    
    print_success "All dependencies are available"
}

# Function to install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    
    # Install Python dependencies
    if [ -f "backend/pyproject.toml" ]; then
        print_status "Installing backend dependencies..."
        cd backend && poetry install --no-root && cd ..
    fi

    if [ -f "agent/pyproject.toml" ]; then
        print_status "Installing agent dependencies..."
        cd agent && poetry install --no-root && cd ..
    fi

    # Install Node.js dependencies
    if [ -f "frontend/package.json" ]; then
        print_status "Installing frontend dependencies..."
        cd frontend && npm install && cd ..
    fi
    
    print_success "Dependencies installed"
}

# Function to start services with honcho
start_services() {
    print_status "Starting services with honcho..."
    
    if ! command_exists honcho; then
        print_warning "Honcho not found. Installing..."
        pip install honcho
    fi
    
    # Check if the configuration is valid
    if ! honcho check -f .honcho.yml > /dev/null 2>&1; then
        print_error "Invalid .honcho.yml configuration"
        exit 1
    fi
    
    honcho start -f .honcho.yml
}

# Function to check if Caddy is installed
check_caddy_installed() {
    if ! command -v caddy >/dev/null 2>&1; then
        print_warning "Caddy not found. Installing..."
        if command -v brew >/dev/null 2>&1; then
            brew install caddy
        elif command -v apt-get >/dev/null 2>&1; then
            sudo apt-get update && sudo apt-get install -y caddy
        else
            print_error "Please install Caddy manually: https://caddyserver.com/docs/install"
            exit 1
        fi
    else
        print_success "Caddy is already installed"
    fi
}

# Function to check if Caddyfile exists in project root
check_caddyfile_exists() {
    if [ ! -f "Caddyfile" ]; then
        print_error "Caddyfile not found in project root. Please ensure it exists."
        exit 1
    fi
}

# Function to check if Caddy ports are available
check_caddy_ports() {
    local ports=(80 443)
    local ports_in_use=()
    print_status "Checking if Caddy ports are available..."
    for port in "${ports[@]}"; do
        if command -v lsof >/dev/null 2>&1; then
            if lsof -i :$port >/dev/null 2>&1; then
                ports_in_use+=($port)
            fi
        elif command -v netstat >/dev/null 2>&1; then
            if netstat -an | grep ":$port " | grep LISTEN >/dev/null 2>&1; then
                ports_in_use+=($port)
            fi
        else
            print_warning "Cannot check if port $port is in use (lsof/netstat not available)"
        fi
    done
    if [ ${#ports_in_use[@]} -gt 0 ]; then
        print_warning "The following ports are already in use: ${ports_in_use[*]}"
        print_warning "This might prevent Caddy from starting properly"
        # read -p "Continue anyway? (y/N): " -n 1 -r
        # echo
        # if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        #     exit 1
        # fi
    else
        print_success "Caddy ports are available"
    fi
}

# Function to start services with domain support
start_services_with_domain() {
    print_status "Checking Caddy requirements..."
    check_caddy_installed
    check_caddyfile_exists
    check_caddy_ports
    print_status "Starting services with domain support..."
    
    # Start all services including Caddy with honcho
    CADDY_ENABLED=1 honcho start -f .honcho.yml
}

# Function to stop services
stop_services() {
    print_status "Stopping services..."
    
    # Kill honcho processes
    pkill -f honcho || true
    
    # Kill Caddy processes
    pkill caddy || true
    
    print_success "Services stopped"
}

# Function to show help
show_help() {
    echo "Development startup script for causal-ai-product"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start       Start services using honcho"
    echo "  stop        Stop running services"
    echo "  install     Install dependencies only"
    echo "  help        Show this help message"
    echo ""
    echo "Options:"
    echo "  --with-domain    Start services with Caddy proxy for domain access"
    echo ""
    echo "Examples:"
    echo "  $0 start              # Start services on localhost"
    echo "  $0 start --with-domain # Start services with domain access"
    echo "  $0 stop               # Stop all services"
    echo "  $0 install            # Install dependencies"
    echo ""
    list_services
    echo "Service Access URLs:"
    list_service_urls
    echo "Domain Access URLs (when using --with-domain):"
    list_service_urls true
}

# Main script logic
main() {
    local command="${1:-start}"
    local with_domain=false
    
    # Check for --with-domain option
    if [[ "$2" == "--with-domain" ]]; then
        with_domain=true
    fi
    
    case "$command" in
        "start")
            check_dependencies
            install_dependencies
            if [ "$with_domain" = true ]; then
                start_services_with_domain
            else
                start_services
            fi
            ;;
        "stop")
            stop_services
            ;;
        "install")
            check_dependencies
            install_dependencies
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            print_error "Unknown command: $command"
            show_help
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@" 