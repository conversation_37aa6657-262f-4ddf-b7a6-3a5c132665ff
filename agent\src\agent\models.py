"""Model classes for the agent module."""

import json
import logging
import os
import re
from typing import Any, Dict, List, Optional

from pydantic import BaseModel

from src.services.cache import Cache

logger = logging.getLogger(__name__)


class ActionModel(BaseModel):
    """Model for agent actions."""

    name: str
    reason: str


class AgentResponse(BaseModel):
    """Response from the agent including thought process."""

    intent: str
    follow_up_question: bool
    thought: str
    action: Optional[ActionModel] = None
    answer: Optional[str] = None
    user_query: Optional[str] = None

    @classmethod
    def from_llm_response(cls, response_text: str) -> "AgentResponse":
        """Create AgentResponse from LLM response text."""
        strict_pattern = r"```json\n(.*?)\n```"
        match = re.search(strict_pattern, response_text, re.DOTALL)
        if not match:
            flexible_pattern = r"```json\n\s*(.*?)\s*\n\s*```"
            match = re.search(flexible_pattern, response_text, re.DOTALL)

        if match:
            try:
                json_str = "\n".join(
                    line.lstrip() for line in match.group(1).split("\n")
                )
                data = json.loads(json_str)

                # Handle legacy action format if it has input field
                if data.get("action") and isinstance(data["action"], dict):
                    action_data = data["action"]
                    if "input" in action_data:
                        # Remove input field for new schema compatibility
                        action_data = {
                            k: v for k, v in action_data.items() if k != "input"
                        }
                        data["action"] = action_data

                return cls(**data)
            except Exception as e:
                logger.error(f"Error parsing LLM response: {e}")
                raise ValueError(f"Error parsing LLM response: {e}")
        else:
            logger.error("No JSON found in LLM response")
            logger.error(f"LLM response: {response_text}")
            raise ValueError("No JSON found in LLM response")


class HistoryEntry(BaseModel):
    """Entry in the agent's history."""

    intent: str
    follow_up_question: bool
    thought: str
    action: Optional[ActionModel] = None  # Updated to use ActionModel
    observation: Optional[str] = None
    answer: Optional[str] = None
    iteration: Optional[int] = None


class LLMOutput(BaseModel):
    """Track LLM outputs for each iteration."""

    iteration: int
    prompt: str
    response: str
    intent: str
    follow_up_question: bool
    parsed_response: Optional[AgentResponse] = None
    thinking_time_seconds: Optional[float] = None  # Time for LLM to generate response
    action_time_seconds: Optional[float] = (
        None  # Time for executing the pipeline action
    )
    input_tokens: Optional[int] = None  # Number of tokens in the prompt
    output_tokens: Optional[int] = None  # Number of tokens in the response

    def to_dict(self) -> Dict[str, Any]:
        """Convert LLMOutput to a dictionary."""
        return {
            "iteration": self.iteration,
            "prompt": self.prompt,
            "intent": self.intent,
            "follow_up_question": self.follow_up_question,
            "thought": (
                self.parsed_response.thought
                if self.parsed_response
                else "Failed to parse response"
            ),
            "action": self.parsed_response.action if self.parsed_response else None,
            "answer": self.parsed_response.answer if self.parsed_response else None,
            "thinking_time_seconds": self.thinking_time_seconds,
            "action_time_seconds": self.action_time_seconds,
            "input_tokens": self.input_tokens,
            "output_tokens": self.output_tokens,
        }


class ConversationEntry(BaseModel):
    """Entry in conversation history."""

    user: str
    agent: Optional[str] = None


class AgentCache:
    """Cache for agent history and conversation data."""

    def __init__(self):
        """Initialize the cache."""
        self.redis_enabled = os.getenv("REDIS_CACHE_ENABLED", "false").lower() == "true"
        self.history: Dict[str, List[HistoryEntry]] = {}
        self.conversation_history: Dict[str, List[ConversationEntry]] = {}

        if self.redis_enabled:
            try:
                self.redis_cache = Cache()
            except Exception as e:
                self.redis_cache = None
                self.redis_enabled = False
                logger.warning(f"Redis cache is not enabled: {e}")
        else:
            self.redis_cache = None

    def get_or_init_history(self, key: str) -> List[HistoryEntry]:
        """Get the cached history.
        
        Filters out entries without answers (failed executions).
        """
        if self.redis_enabled and self.redis_cache:
            try:
                cached_data = self.redis_cache.get_json(f"agent_history:{key}")
                if cached_data:
                    all_entries = [HistoryEntry(**item) for item in cached_data]
                    return [entry for entry in all_entries if entry.answer is not None]
            except Exception as e:
                logger.error(f"Error getting history: {e}")

        if key not in self.history:
            self.history[key] = []
        return [entry for entry in self.history[key] if entry.answer is not None]

    def update_history(self, key: str, value: List[HistoryEntry]) -> None:
        """Set the cached history."""
        self.history[key] = value

        if self.redis_enabled and self.redis_cache:
            try:
                serialized_data = [item.model_dump() for item in value]
                self.redis_cache.store_json(f"agent_history:{key}", serialized_data)
            except Exception as e:
                logger.error(f"Error updating history: {e}")

    def get_or_init_conversation_history(self, key: str) -> List[ConversationEntry]:
        """Get the cached conversation history.
        
        Filters out entries without agent responses (failed executions).
        """
        if self.redis_enabled and self.redis_cache:
            try:
                cached_data = self.redis_cache.get_json(f"agent_conversation:{key}")
                if cached_data:
                    all_entries = [ConversationEntry(**item) for item in cached_data]
                    return [entry for entry in all_entries if entry.agent is not None]
            except Exception as e:
                logger.error(f"Error getting conversation history: {e}")

        if key not in self.conversation_history:
            self.conversation_history[key] = []
        return [entry for entry in self.conversation_history[key] if entry.agent is not None]

    def update_conversation_history(
        self, key: str, value: List[ConversationEntry]
    ) -> None:
        """Set the cached conversation history."""
        self.conversation_history[key] = value

        if self.redis_enabled and self.redis_cache:
            try:
                serialized_data = [item.model_dump() for item in value]
                self.redis_cache.store_json(f"agent_conversation:{key}", serialized_data)
            except Exception as e:
                logger.error(f"Error updating conversation history: {e}")

    def clear_cache(self, key: str) -> None:
        """Clear cache for a specific conversation."""
        if key in self.history:
            del self.history[key]
        if key in self.conversation_history:
            del self.conversation_history[key]

        if self.redis_enabled and self.redis_cache:
            try:
                self.redis_cache.store_json(f"agent_history:{key}", [])
                self.redis_cache.store_json(f"agent_conversation:{key}", [])
            except Exception as e:
                logger.error(f"Error clearing cache: {e}")

    def clear_all_caches(self) -> None:
        """Clear all cached data."""
        self.history.clear()
        self.conversation_history.clear()

        if self.redis_enabled and self.redis_cache:
            try:
                history_keys = self.redis_cache.get_all_by_prefix("agent_history:")
                conversation_keys = self.redis_cache.get_all_by_prefix("agent_conversation:")

                for key in history_keys + conversation_keys:
                    self.redis_cache.store_json(key, [])
            except Exception as e:
                logger.error(f"Error clearing all caches: {e}")


class AgentState(BaseModel):
    """Current state of the agent during execution."""

    conversation_id: str
    current_iteration: int = 0
    max_iterations: int = 5
    query: str  # Original query
    current_query: Optional[str] = None  # Reformulated/current query being processed
    history: List[HistoryEntry] = []
    conversation_history: List[ConversationEntry] = []
    llm_outputs: List[LLMOutput] = []
    cached_papers: List[Dict[str, Any]] = []

    class Config:
        arbitrary_types_allowed = True

    def add_to_history(
        self,
        intent: str,
        follow_up_question: bool,
        thought: str,
        action: Optional[Dict[str, Any]] = None,
        observation: Optional[str] = None,
        answer: Optional[str] = None,
        iteration: Optional[int] = None,
    ) -> None:
        """Add an entry to history."""
        entry = HistoryEntry(
            intent=intent,
            follow_up_question=follow_up_question,
            thought=thought,
            action=action,
            observation=observation,
            answer=answer,
            iteration=iteration,
        )
        self.history.append(entry)

    def add_conversation_entry(
        self, user_message: str, agent_response: Optional[str] = None
    ) -> None:
        """Add a conversation entry."""
        entry = ConversationEntry(user=user_message, agent=agent_response)
        self.conversation_history.append(entry)

    def update_last_conversation_entry(self, agent_response: str) -> None:
        """Update the last conversation entry with agent response."""
        if self.conversation_history:
            self.conversation_history[-1].agent = agent_response

    def format_history(self) -> str:
        """Format history for prompt."""
        formatted = []
        for entry in self.history:
            formatted.append(f"=========Iteration: {entry.iteration}=========")
            formatted.append(f"Thought: {entry.thought}")
            if entry.action:
                # Handle ActionModel properly
                formatted.append(
                    f"Action: {entry.action.name} - Objective: {entry.action.reason}"
                )
            if entry.observation:
                formatted.append(f"Observation: {entry.observation}")
            if entry.answer:
                formatted.append(f"Answer: {entry.answer}")
            formatted.append(f"=========End of Iteration: {entry.iteration}=========")
        return "\n".join(formatted)

    def format_conversation_history(self) -> str:
        """Format conversation history for prompt."""
        formatted = []
        for entry in self.conversation_history:
            formatted.append(f"User: {entry.user}")
            if entry.agent:
                formatted.append(f"Agent: {entry.agent}")
        return "\n".join(formatted)

    def get_llm_output(self, iteration: int) -> Optional[LLMOutput]:
        """Get LLM output for a specific iteration."""
        for output in self.llm_outputs:
            if output.iteration == iteration:
                return output
        return None

    def get_recent_conversation_history(
        self, max_entries: int = 4
    ) -> List[ConversationEntry]:
        """Get the last few conversation entries for context."""
        return (
            self.conversation_history[-max_entries:]
            if len(self.conversation_history) > max_entries
            else self.conversation_history
        )
