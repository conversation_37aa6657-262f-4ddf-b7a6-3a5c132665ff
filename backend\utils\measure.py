import time, os
from functools import wraps
from dotenv import load_dotenv

load_dotenv()

in_debug = os.getenv("DEBUG", "false").lower() == "true"


def measure_time(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        if not in_debug:
            result = func(*args, **kwargs)
            return result

        start_time = time.perf_counter()
        result = func(*args, **kwargs)
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        print(
            f"Function '{func.__name__}' took {execution_time:.4f} seconds to execute"
        )
        return result

    return wrapper


def measure_async_time(func):
    @wraps(func)
    async def wrapper(*args, **kwargs):
        if not in_debug:
            result = await func(*args, **kwargs)
            return result

        start_time = time.perf_counter()
        result = await func(*args, **kwargs)
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        print(
            f"Function '{func.__name__}' took {execution_time:.4f} seconds to execute"
        )
        return result

    return wrapper
