import { useState, useEffect } from "react";
import { get } from "../services/apiService";
import {
  Message
} from "../types/ConversationTypes";
import { API_ERROR_MESSAGE } from "../utils/labels";

interface PlotAndSourcesData {
  fullMessageInfo: Message | null;
  hasAttemptedGetInfoFetch: boolean;
}

interface UsePlotAndSourcesDataProps {
  conversationId: string;
  informationMessageId: string;
  hasStreamingStartedForDataFetch: boolean;
}

const usePlotAndSourcesData = ({
  conversationId,
  informationMessageId,
  hasStreamingStartedForDataFetch,
}: UsePlotAndSourcesDataProps): PlotAndSourcesData => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [fullMessageInfo, setFullMessageInfo] = useState<Message | null>(null);
  const [hasAttemptedGetInfoFetch, setHasAttemptedGetInfoFetch] = useState(false);

  const getMessageInfo = async (
    currentConversationId: string,
    messageId: string
  ) => {
    setLoading(true);
    setError(null);
    setFullMessageInfo(null);
    setHasAttemptedGetInfoFetch(false);

    try {
      const response: any = await get(
        `/conversations/${currentConversationId}`
      );

      if (response?.success) {
        const message = response.data.messages.find((message: Message) => message.id === messageId);
        setFullMessageInfo(message || null);
        setHasAttemptedGetInfoFetch(true);
      } else {
        setError(API_ERROR_MESSAGE);
        setHasAttemptedGetInfoFetch(true);
      }
    } catch (err) {
      console.error("getMessageInfo error:", err);
      setError(API_ERROR_MESSAGE);
      setHasAttemptedGetInfoFetch(true);
    } finally {
      setLoading(false);
      setHasAttemptedGetInfoFetch(true);
    }
  };

  useEffect(() => {
    if (conversationId && informationMessageId && hasStreamingStartedForDataFetch) {
      getMessageInfo(conversationId, informationMessageId);
    }
  }, [conversationId, informationMessageId, hasStreamingStartedForDataFetch]);

  return { fullMessageInfo, hasAttemptedGetInfoFetch };
};

export default usePlotAndSourcesData;