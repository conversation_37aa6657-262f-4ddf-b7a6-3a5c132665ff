import { useState, useRef, useEffect, MouseEvent } from 'react';
import {
    <PERSON>rid, IconButton, Tooltip, Dialog, DialogTitle,
    DialogContent, DialogActions, Button, TextField, Chip, Box, Typography
} from '@mui/material';
import ThumbUpOffAltIcon from '@mui/icons-material/ThumbUpOffAlt';
import ThumbDownOffAltIcon from '@mui/icons-material/ThumbDownOffAlt';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import CloseIcon from '@mui/icons-material/Close';
import { post } from "../../../services/apiService";
import { useTheme } from '@mui/material/styles';
import { THUMBS_FEEDBACK_GOOD_RESPONSE_LABEL, THUMBS_FEEDBACK_BAD_RESPONSE_LABEL } from "../../../utils/labels";
import { useSnackbar } from '../../Common/Snackbar/SnackbarContext';
import { useIsMobile } from "../../Layout/MobileUtils";

interface ThumbsFeedbackProps {
    conversationId: string;
    messageId: string;
    visible: boolean;
    textToCopy?: string;
}

interface ThumbsFeedback {
    reaction: string;
    feedbackText?: string;
    selectedChips?: string;
}

export default function ThumbsFeedback({
    conversationId,
    messageId,
    visible,
    textToCopy
}: ThumbsFeedbackProps) {
    const [selectedReaction, setSelectedReaction] = useState<string | null>(null);
    const [feedbackModalOpen, setFeedbackModalOpen] = useState(false);
    const [feedbackText, setFeedbackText] = useState('');
    const [selectedChips, setSelectedChips] = useState<string[]>([]);
    const snackbarTimeout = useRef<NodeJS.Timeout | null>(null);
    const hasFocus = useRef(true);
    const hasSubmittedThumbsDown = useRef(false);
    const theme = useTheme();
    const isMobile = useIsMobile();
    const { openSnackbar } = useSnackbar();

    const chipLabels = [
        "Inaccurate data", "Answer too long", "Answer too short",
        "Offensive", "Misrepresented findings", "Missing data", "Difficult to read"
    ];

    const textFieldStyles = {
        "& .MuiInputBase-root": {
            borderRadius: "4px",
            "&:hover .MuiOutlinedInput-notchedOutline": {
                borderColor: theme.palette.text.primary,
            },
            "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                borderColor: theme.palette.text.primary,
                borderWidth: "1px",
                boxShadow: "none",
            },
        },
        "& .MuiInputLabel-root": {
            color: theme.palette.text.primary,
        },
        "& .MuiOutlinedInput-notchedOutline": {
            border: "1px solid #D4E4FC",
            transition: "border-color 0.3s ease",
        },
        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
            border: "1px solid",
            borderColor: theme.palette.text.primary,
        },
        "&:hover .MuiOutlinedInput-notchedOutline": {
            border: "1px solid",
            borderColor: theme.palette.text.primary,
        },
    };

    const handleChipClick = (label: string) => {
        setSelectedChips(prev =>
            prev.includes(label) ? prev.filter(c => c !== label) : [...prev, label]
        );
    };

    const showSnackbar = (message: string) => {
        if (!hasFocus.current) return;
        if (snackbarTimeout.current) {
            clearTimeout(snackbarTimeout.current);
        }
        openSnackbar(message);
    };

    const postFeedback = async (reaction: string) => {
        const body: ThumbsFeedback = {
            reaction,
            feedbackText: feedbackText.trim() || '',
            selectedChips: selectedChips.length ? selectedChips.join(', ') : ''
        };

        try {
            await post(`/conversations/${conversationId}/messages/${messageId}/feedback`, body);
        } catch (error) {
            console.error('Error posting reaction:', error);
        }
    };

    const handleThumbsUp = (event: MouseEvent) => {
        setSelectedReaction('thumbs-up');
        showSnackbar("Thank you for your feedback");
        postFeedback('thumbs-up');
        if (event.currentTarget instanceof HTMLElement) event.currentTarget.blur();
    };

    const handleThumbsDownClick = (event: MouseEvent) => {
        setSelectedReaction('thumbs-down');
        setFeedbackModalOpen(true);
        if (event.currentTarget instanceof HTMLElement) event.currentTarget.blur();
    };

    const handleSubmitThumbsDown = async () => {
        if (hasSubmittedThumbsDown.current) return;
        hasSubmittedThumbsDown.current = true;
        setFeedbackModalOpen(false);
        showSnackbar("Feedback successfully submitted");
        await postFeedback('thumbs-down');
        setTimeout(() => {
            hasSubmittedThumbsDown.current = false;
        }, 3000);
    };

    const copyTextHandler = async (event: MouseEvent) => {
        try {
            if (textToCopy) {
                await navigator.clipboard.writeText(textToCopy);
                showSnackbar("Text copied to clipboard");
            }
        } catch {
            showSnackbar("Failed to copy to clip/board.");
        }
        if (event.currentTarget instanceof HTMLElement) event.currentTarget.blur();
    };

    useEffect(() => {
        const handleWindowBlur = () => hasFocus.current = false;
        const handleWindowFocus = () => hasFocus.current = true;

        window.addEventListener('blur', handleWindowBlur);
        window.addEventListener('focus', handleWindowFocus);

        return () => {
            window.removeEventListener('blur', handleWindowBlur);
            window.removeEventListener('focus', handleWindowFocus);
        };
    }, []);

    useEffect(() => {
        if (!feedbackModalOpen) {
            setSelectedChips([]);
            setFeedbackText('');
        }
    }, [feedbackModalOpen]);

    return (
        <Grid
            container
            justifyContent="flex-end"
            alignItems="center"
            data-feedback-trigger={messageId}
            style={{
                opacity: visible ? 1 : 0,
                pointerEvents: visible ? 'auto' : 'none',
                transition: 'opacity 0.2s ease-in-out',
                height: '40px',
            }}
        >
            <Grid item sx={{ marginRight: isMobile ? '0px' : '16px' }}>
                <Tooltip title={THUMBS_FEEDBACK_GOOD_RESPONSE_LABEL} placement="top">
                    <IconButton
                        onClick={handleThumbsUp}
                        sx={{
                            p: isMobile ? '4px' : '8px',
                            color: selectedReaction === 'thumbs-up' ? theme.components.icon.hoverBold : theme.components.icon.defaultLight,
                            "&:hover, &.Mui-focusVisible": {
                                color: theme.components.icon.hoverBold,
                            }
                        }}
                    >
                        <ThumbUpOffAltIcon fontSize="small" />
                    </IconButton>
                </Tooltip>
            </Grid>

            <Grid item sx={{ marginRight: isMobile ? '0px' : '16px' }}>
                <Tooltip title={THUMBS_FEEDBACK_BAD_RESPONSE_LABEL} placement="top">
                    <IconButton
                        onClick={handleThumbsDownClick}
                        sx={{
                            color: selectedReaction === 'thumbs-down' ? theme.components.icon.hoverBold : theme.components.icon.defaultLight,
                            "&:hover, &.Mui-focusVisible": {
                                color: theme.components.icon.hoverBold,
                            }
                        }}
                    >
                        <ThumbDownOffAltIcon fontSize="small" />
                    </IconButton>
                </Tooltip>
            </Grid>

            {textToCopy && (
                <Grid item>
                    <Tooltip title="Copy" placement="top">
                        <IconButton
                            onClick={copyTextHandler}
                            sx={{
                                color: theme.components.icon.defaultLight,
                                "&:hover, &.Mui-focusVisible": {
                                    color: theme.components.icon.hoverBold,
                                }
                            }}
                        >
                            <ContentCopyIcon fontSize="small" />
                        </IconButton>
                    </Tooltip>
                </Grid>
            )}

            <Dialog
                open={feedbackModalOpen}
                onClose={() => setFeedbackModalOpen(false)}
                fullWidth
                maxWidth="sm"
                PaperProps={{
                    sx: {
                        background: theme.palette.background.default,
                        color: theme.palette.text.primary,
                        p: '24px',
                        borderRadius: '24px',
                        boxShadow: '0px 1px 3px 0px rgba(0, 0, 0, 0.05), 0px 1px 1px 0px rgba(0, 0, 0, 0.05), 0px 2px 1px -1px rgba(0, 0, 0, 0.05)'
                    },
                }}
            >
                <DialogTitle sx={{ padding: '16px 0', mb: '24px' }}>
                    <Typography variant="h5" sx={{ fontSize: '30px' }}>Answer feedback</Typography>
                    <IconButton aria-label="close" onClick={() => setFeedbackModalOpen(false)} sx={{ position: 'absolute', right: 8, top: 8 }}>
                        <CloseIcon />
                    </IconButton>
                </DialogTitle>
                <DialogContent sx={{ padding: '24px 0' }}>
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: '36px' }}>
                        {chipLabels.map((label, index) => (
                            <Chip
                                key={`chip-${index}`}
                                clickable
                                label={label}
                                onClick={() => handleChipClick(label)}
                                sx={{
                                    padding: "3px 6px",
                                    maxWidth: '100%',
                                    color: theme.palette.text.primary,
                                    backgroundColor: selectedChips.includes(label)
                                        ? theme.elevation.paperElevationTwentyFour
                                        : theme.elevation.paperElevationTwo,
                                    '&.MuiChip-clickable:hover': {
                                        backgroundColor: selectedChips.includes(label)
                                            ? theme.elevation.paperElevationTwentyFour
                                            : theme.elevation.paperElevationTwo,
                                    },
                                    '&.MuiChip-clickable:focus': {
                                        backgroundColor: selectedChips.includes(label)
                                            ? theme.elevation.paperElevationTwentyFour
                                            : theme.elevation.paperElevationTwo,
                                    },
                                }}
                            />
                        ))}
                    </Box>
                    <TextField
                        InputLabelProps={{ shrink: true }}
                        label="Feedback (optional)"
                        placeholder="Add specific details"
                        multiline
                        rows={2}
                        fullWidth
                        value={feedbackText}
                        onChange={(e) => setFeedbackText(e.target.value)}
                        sx={{ ...textFieldStyles, mb: '24px' }}
                    />
                </DialogContent>
                <DialogActions sx={{ justifyContent: 'flex-end' }}>
                    <Button
                        onClick={handleSubmitThumbsDown}
                        variant="contained"
                        size="large"
                        sx={{
                            padding: '8px 22px',
                            borderRadius: '40px',
                            fontWeight: 600,
                            fontSize: '15px',
                            background: theme.palette.action.active,
                            fontFamily: "Roboto",
                            boxShadow: '0px 1px 5px rgba(0,0,0,0.05), 0px 2px 2px rgba(0,0,0,0.05), 0px 3px 1px -2px rgba(0,0,0,0.05)',
                        }}
                    >
                        Submit
                    </Button>
                </DialogActions>
            </Dialog>
        </Grid>
    );
}