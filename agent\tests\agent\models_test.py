import os
import unittest
from unittest.mock import Mock, patch

from src.agent.models import (
    ActionModel,
    AgentResponse,
    HistoryEntry,
    ConversationEntry,
    AgentCache,
    AgentState,
    LLMOutput,
)


class TestActionModel(unittest.TestCase):
    """Test ActionModel basic functionality."""

    def test_action_model_creation(self):
        """Test ActionModel can be created with required fields."""
        action = ActionModel(name="search", reason="Find relevant papers")
        self.assertEqual(action.name, "search")
        self.assertEqual(action.reason, "Find relevant papers")


class TestAgentResponse(unittest.TestCase):
    """Test AgentResponse parsing from LLM responses."""

    def test_from_llm_response_valid_json(self):
        """Test parsing valid JSON response from LLM."""
        response_text = '''```json
        {
            "intent": "search",
            "follow_up_question": false,
            "thought": "I need to search for papers",
            "action": {"name": "search", "reason": "Find papers"},
            "answer": null
        }
        ```'''

        response = AgentResponse.from_llm_response(response_text)
        self.assertEqual(response.intent, "search")
        self.assertFalse(response.follow_up_question)
        self.assertEqual(response.thought, "I need to search for papers")
        self.assertEqual(response.action.name, "search")
        self.assertEqual(response.action.reason, "Find papers")

    def test_from_llm_response_legacy_action_format(self):
        """Test parsing legacy action format with input field."""
        response_text = '''```json
        {
            "intent": "search",
            "follow_up_question": false,
            "thought": "I need to search",
            "action": {"name": "search", "reason": "Find papers", "input": "deprecated"},
            "answer": null
        }
        ```'''

        response = AgentResponse.from_llm_response(response_text)
        self.assertEqual(response.action.name, "search")
        self.assertEqual(response.action.reason, "Find papers")
        # Input field should be removed

    def test_from_llm_response_no_json(self):
        """Test error handling when no JSON is found."""
        response_text = "This is just plain text without JSON"

        with self.assertRaises(ValueError) as context:
            AgentResponse.from_llm_response(response_text)
        self.assertIn("No JSON found", str(context.exception))

    def test_from_llm_response_invalid_json(self):
        """Test error handling for invalid JSON."""
        response_text = '''```json
        {
            "intent": "search",
            "invalid": json
        }
        ```'''

        with self.assertRaises(ValueError) as context:
            AgentResponse.from_llm_response(response_text)
        self.assertIn("Error parsing LLM response", str(context.exception))


class TestAgentCacheInMemory(unittest.TestCase):
    """Test AgentCache behavior when Redis is disabled."""

    def setUp(self):
        """Set up test with Redis disabled."""
        # Ensure Redis is disabled for these tests
        with patch.dict(os.environ, {"REDIS_CACHE_ENABLED": "false"}):
            self.cache = AgentCache()

    def test_redis_disabled_initialization(self):
        """Test AgentCache initializes with Redis disabled."""
        self.assertFalse(self.cache.redis_enabled)
        self.assertIsNone(self.cache.redis_cache)

    def test_history_operations_in_memory_only(self):
        """Test history operations use only in-memory storage when Redis disabled."""
        key = "test_conversation"

        # Initially empty
        history = self.cache.get_or_init_history(key)
        self.assertEqual(history, [])

        # Add history entry
        entries = [HistoryEntry(
            intent="search",
            follow_up_question=False,
            thought="Test thought",
            answer="Test answer",  # Add answer so it passes the filter
            iteration=1
        )]
        self.cache.update_history(key, entries)

        # Retrieve history
        retrieved = self.cache.get_or_init_history(key)
        self.assertEqual(len(retrieved), 1)
        self.assertEqual(retrieved[0].intent, "search")
        self.assertEqual(retrieved[0].thought, "Test thought")

    def test_conversation_history_operations_in_memory_only(self):
        """Test conversation history operations use only in-memory storage."""
        key = "test_conversation"

        # Initially empty
        conv_history = self.cache.get_or_init_conversation_history(key)
        self.assertEqual(conv_history, [])

        # Add conversation entries
        entries = [ConversationEntry(
            user="What is AI?",
            agent="AI is artificial intelligence"
        )]
        self.cache.update_conversation_history(key, entries)

        # Retrieve conversation history
        retrieved = self.cache.get_or_init_conversation_history(key)
        self.assertEqual(len(retrieved), 1)
        self.assertEqual(retrieved[0].user, "What is AI?")
        self.assertEqual(retrieved[0].agent, "AI is artificial intelligence")

    def test_clear_cache_in_memory_only(self):
        """Test clearing specific cache entries."""
        key = "test_conversation"

        # Add data
        self.cache.update_history(key, [HistoryEntry(
            intent="test", follow_up_question=False, thought="test", iteration=1
        )])
        self.cache.update_conversation_history(key, [ConversationEntry(
            user="test", agent="response"
        )])

        # Clear cache
        self.cache.clear_cache(key)

        # Verify data is cleared from in-memory storage
        self.assertNotIn(key, self.cache.history)
        self.assertNotIn(key, self.cache.conversation_history)

    def test_history_filtering_entries_without_answers(self):
        """Test that get_or_init_history filters out entries without answers."""
        key = "test_conversation"

        # Create entries with and without answers
        entries = [
            HistoryEntry(
                intent="search",
                follow_up_question=False,
                thought="Failed execution",
                answer=None,  # No answer - should be filtered out
                iteration=1
            ),
            HistoryEntry(
                intent="search",
                follow_up_question=False,
                thought="Successful execution",
                answer="This is a successful answer",  # Has answer - should be included
                iteration=2
            ),
            HistoryEntry(
                intent="analyze",
                follow_up_question=False,
                thought="Another failed execution",
                answer=None,  # No answer - should be filtered out
                iteration=3
            )
        ]

        # Store all entries (including failed ones)
        self.cache.update_history(key, entries)

        # Retrieve history - should only get entries with answers
        retrieved = self.cache.get_or_init_history(key)
        # Should only return the one entry with an answer
        self.assertEqual(len(retrieved), 1)
        self.assertEqual(retrieved[0].thought, "Successful execution")
        self.assertEqual(retrieved[0].answer, "This is a successful answer")
        self.assertEqual(retrieved[0].iteration, 2)

    def test_conversation_history_filtering_entries_without_agent_response(self):
        """Test get_or_init_conversation_history filters out entries without agent responses."""
        key = "test_conversation"

        # Create entries with and without agent responses
        entries = [
            ConversationEntry(
                user="What is AI?",
                agent=None  # No agent response - should be filtered out
            ),
            ConversationEntry(
                user="Tell me about machine learning",
                agent="Machine learning is a subset of AI"  # Has agent response
            ),
            ConversationEntry(
                user="How does deep learning work?",
                agent=None  # No agent response - should be filtered out
            ),
            ConversationEntry(
                user="What is neural networks?",
                agent="Neural networks are computing systems inspired by biological neural networks"
            )
        ]

        # Store all entries (including failed ones)
        self.cache.update_conversation_history(key, entries)

        # Retrieve conversation history - should only get entries with agent responses
        retrieved = self.cache.get_or_init_conversation_history(key)
        # Should only return the two entries with agent responses
        self.assertEqual(len(retrieved), 2)
        self.assertEqual(retrieved[0].user, "Tell me about machine learning")
        self.assertEqual(retrieved[0].agent, "Machine learning is a subset of AI")
        self.assertEqual(retrieved[1].user, "What is neural networks?")
        self.assertEqual(
            retrieved[1].agent,
            "Neural networks are computing systems inspired by biological neural networks"
        )

    def test_history_filtering_all_entries_filtered_out(self):
        """Test get_or_init_history returns empty list when all entries lack answers."""
        key = "test_conversation"

        # Create entries without any answers
        entries = [
            HistoryEntry(
                intent="search",
                follow_up_question=False,
                thought="Failed execution 1",
                answer=None,
                iteration=1
            ),
            HistoryEntry(
                intent="analyze",
                follow_up_question=False,
                thought="Failed execution 2",
                answer=None,
                iteration=2
            )
        ]

        # Store entries without answers
        self.cache.update_history(key, entries)

        # Retrieve history - should get empty list
        retrieved = self.cache.get_or_init_history(key)
        self.assertEqual(len(retrieved), 0)

    def test_conversation_history_filtering_all_entries_filtered_out(self):
        """Test get_or_init_conversation_history returns empty list when all entries lack agent responses."""
        key = "test_conversation"

        # Create entries without any agent responses
        entries = [
            ConversationEntry(
                user="What is AI?",
                agent=None
            ),
            ConversationEntry(
                user="Tell me about ML",
                agent=None
            )
        ]

        # Store entries without agent responses
        self.cache.update_conversation_history(key, entries)

        # Retrieve conversation history - should get empty list
        retrieved = self.cache.get_or_init_conversation_history(key)
        self.assertEqual(len(retrieved), 0)


class TestAgentCacheWithRedis(unittest.TestCase):
    """Test AgentCache behavior when Redis is enabled."""

    def setUp(self):
        """Set up test with Redis enabled and mocked."""
        self.mock_redis_cache = Mock()

        with patch.dict(os.environ, {"REDIS_CACHE_ENABLED": "true"}):
            with patch('src.agent.models.Cache', return_value=self.mock_redis_cache):
                self.cache = AgentCache()

    def test_redis_enabled_initialization(self):
        """Test AgentCache initializes with Redis enabled."""
        self.assertTrue(self.cache.redis_enabled)
        self.assertIsNotNone(self.cache.redis_cache)

    def test_history_operations_use_redis(self):
        """Test history operations use Redis when enabled."""
        key = "test_conversation"

        # Mock Redis return data
        redis_data = [{
            "intent": "search",
            "follow_up_question": False,
            "thought": "Redis thought",
            "action": None,
            "observation": None,
            "answer": "Redis answer",  # Add answer so it passes the filter
            "iteration": 1
        }]
        self.mock_redis_cache.get_json.return_value = redis_data

        # Get history - should call Redis
        history = self.cache.get_or_init_history(key)
        self.mock_redis_cache.get_json.assert_called_with(f"agent_history:{key}")
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0].thought, "Redis thought")

        # Update history - should call Redis
        new_entries = [HistoryEntry(
            intent="update", follow_up_question=False, thought="new", iteration=2
        )]
        self.cache.update_history(key, new_entries)
        self.mock_redis_cache.store_json.assert_called()

    def test_conversation_history_operations_use_redis(self):
        """Test conversation history operations use Redis when enabled."""
        key = "test_conversation"

        # Mock Redis return data
        redis_data = [{"user": "Redis question", "agent": "Redis answer"}]
        self.mock_redis_cache.get_json.return_value = redis_data

        # Get conversation history - should call Redis
        conv_history = self.cache.get_or_init_conversation_history(key)
        self.mock_redis_cache.get_json.assert_called_with(f"agent_conversation:{key}")
        self.assertEqual(len(conv_history), 1)
        self.assertEqual(conv_history[0].user, "Redis question")

        # Update conversation history - should call Redis
        new_entries = [ConversationEntry(user="new", agent="response")]
        self.cache.update_conversation_history(key, new_entries)
        self.mock_redis_cache.store_json.assert_called()

    def test_redis_fallback_to_memory_on_error(self):
        """Test fallback to in-memory storage when Redis fails."""
        key = "test_conversation"

        # Mock Redis to raise exception
        self.mock_redis_cache.get_json.side_effect = Exception("Redis error")

        # Should fallback to in-memory storage
        history = self.cache.get_or_init_history(key)
        self.assertEqual(history, [])  # Empty in-memory storage

        # Should still work with in-memory storage
        entries = [HistoryEntry(
            intent="fallback",
            follow_up_question=False,
            thought="memory",
            answer="memory answer",
            iteration=1
        )]
        self.cache.update_history(key, entries)

        # Mock Redis to work again
        self.mock_redis_cache.get_json.side_effect = None
        self.mock_redis_cache.get_json.return_value = None

        # Should return in-memory data
        retrieved = self.cache.get_or_init_history(key)
        self.assertEqual(len(retrieved), 1)
        self.assertEqual(retrieved[0].thought, "memory")

    def test_clear_all_caches_with_redis(self):
        """Test clearing all caches calls Redis methods."""
        # Mock Redis prefix search
        self.mock_redis_cache.get_all_by_prefix.side_effect = [
            ["agent_history:conv1", "agent_history:conv2"],
            ["agent_conversation:conv1", "agent_conversation:conv2"]
        ]

        self.cache.clear_all_caches()

        # Should call Redis to get keys and clear them
        self.mock_redis_cache.get_all_by_prefix.assert_any_call("agent_history:")
        self.mock_redis_cache.get_all_by_prefix.assert_any_call("agent_conversation:")
        self.assertEqual(self.mock_redis_cache.store_json.call_count, 4)  # 4 keys cleared

    def test_history_filtering_entries_without_answers_redis(self):
        """Test that get_or_init_history filters out entries without answers when using Redis."""
        key = "test_conversation"

        # Mock Redis return data with mixed entries (some with answers, some without)
        redis_data = [
            {
                "intent": "search",
                "follow_up_question": False,
                "thought": "Failed execution",
                "action": None,
                "observation": None,
                "answer": None,  # No answer - should be filtered out
                "iteration": 1
            },
            {
                "intent": "search",
                "follow_up_question": False,
                "thought": "Successful execution",
                "action": None,
                "observation": None,
                "answer": "This is a successful answer",  # Has answer - should be included
                "iteration": 2
            },
            {
                "intent": "analyze",
                "follow_up_question": False,
                "thought": "Another failed execution",
                "action": None,
                "observation": None,
                "answer": None,  # No answer - should be filtered out
                "iteration": 3
            }
        ]
        self.mock_redis_cache.get_json.return_value = redis_data

        # Get history - should filter out entries without answers
        history = self.cache.get_or_init_history(key)
        # Should only return the one entry with an answer
        self.assertEqual(len(history), 1)
        self.assertEqual(history[0].thought, "Successful execution")
        self.assertEqual(history[0].answer, "This is a successful answer")
        self.assertEqual(history[0].iteration, 2)

    def test_conversation_history_filtering_entries_without_agent_response_redis(self):
        """Test get_or_init_conversation_history filters out entries without agent responses (Redis)."""
        key = "test_conversation"

        # Mock Redis return data with mixed entries (some with agent responses, some without)
        redis_data = [
            {
                "user": "What is AI?",
                "agent": None  # No agent response - should be filtered out
            },
            {
                "user": "Tell me about machine learning",
                "agent": "Machine learning is a subset of AI"  # Has agent response
            },
            {
                "user": "How does deep learning work?",
                "agent": None  # No agent response - should be filtered out
            },
            {
                "user": "What is neural networks?",
                "agent": "Neural networks are computing systems inspired by biological neural networks"
            }
        ]
        self.mock_redis_cache.get_json.return_value = redis_data

        # Get conversation history - should filter out entries without agent responses
        conv_history = self.cache.get_or_init_conversation_history(key)
        # Should only return the two entries with agent responses
        self.assertEqual(len(conv_history), 2)
        self.assertEqual(conv_history[0].user, "Tell me about machine learning")
        self.assertEqual(conv_history[0].agent, "Machine learning is a subset of AI")
        self.assertEqual(conv_history[1].user, "What is neural networks?")
        self.assertEqual(
            conv_history[1].agent,
            "Neural networks are computing systems inspired by biological neural networks"
        )

    def test_history_filtering_all_entries_filtered_out_redis(self):
        """Test get_or_init_history returns empty list when all Redis entries lack answers."""
        key = "test_conversation"

        # Mock Redis return data with entries that have no answers
        redis_data = [
            {
                "intent": "search",
                "follow_up_question": False,
                "thought": "Failed execution 1",
                "action": None,
                "observation": None,
                "answer": None,
                "iteration": 1
            },
            {
                "intent": "analyze",
                "follow_up_question": False,
                "thought": "Failed execution 2",
                "action": None,
                "observation": None,
                "answer": None,
                "iteration": 2
            }
        ]
        self.mock_redis_cache.get_json.return_value = redis_data

        # Get history - should return empty list since all entries are filtered out
        history = self.cache.get_or_init_history(key)
        self.assertEqual(len(history), 0)

    def test_conversation_history_filtering_all_entries_filtered_out_redis(self):
        """Test get_or_init_conversation_history returns empty list when all Redis entries lack responses."""
        key = "test_conversation"

        # Mock Redis return data with entries that have no agent responses
        redis_data = [
            {
                "user": "What is AI?",
                "agent": None
            },
            {
                "user": "Tell me about ML",
                "agent": None
            }
        ]
        self.mock_redis_cache.get_json.return_value = redis_data

        # Get conversation history - should return empty list since all entries are filtered out
        conv_history = self.cache.get_or_init_conversation_history(key)
        self.assertEqual(len(conv_history), 0)


class TestAgentState(unittest.TestCase):
    """Test AgentState functionality and methods."""

    def setUp(self):
        """Set up test AgentState."""
        self.state = AgentState(
            conversation_id="test_conv",
            query="What is machine learning?",
            max_iterations=3
        )

    def test_agent_state_initialization(self):
        """Test AgentState initializes with correct defaults."""
        self.assertEqual(self.state.conversation_id, "test_conv")
        self.assertEqual(self.state.query, "What is machine learning?")
        self.assertEqual(self.state.current_iteration, 0)
        self.assertEqual(self.state.max_iterations, 3)
        self.assertEqual(self.state.history, [])
        self.assertEqual(self.state.conversation_history, [])
        self.assertEqual(self.state.llm_outputs, [])

    def test_add_to_history(self):
        """Test adding entries to history."""
        self.state.add_to_history(
            intent="search",
            follow_up_question=False,
            thought="Need to search",
            action={"name": "search", "reason": "Find papers"},
            observation="Found 5 papers",
            answer="Here are the papers",
            iteration=1
        )

        self.assertEqual(len(self.state.history), 1)
        entry = self.state.history[0]
        self.assertEqual(entry.intent, "search")
        self.assertEqual(entry.thought, "Need to search")
        self.assertEqual(entry.observation, "Found 5 papers")
        self.assertEqual(entry.iteration, 1)

    def test_conversation_entry_operations(self):
        """Test conversation entry add and update operations."""
        # Add conversation entry
        self.state.add_conversation_entry("What is AI?")
        self.assertEqual(len(self.state.conversation_history), 1)
        self.assertEqual(self.state.conversation_history[0].user, "What is AI?")
        self.assertIsNone(self.state.conversation_history[0].agent)

        # Update last entry with agent response
        self.state.update_last_conversation_entry("AI is artificial intelligence")
        self.assertEqual(self.state.conversation_history[0].agent, "AI is artificial intelligence")

    def test_format_history(self):
        """Test history formatting for prompts."""
        self.state.add_to_history(
            intent="search",
            follow_up_question=False,
            thought="I need to search",
            action=ActionModel(name="search", reason="Find papers"),
            observation="Found papers",
            answer="Here are the results",
            iteration=1
        )

        formatted = self.state.format_history()

        self.assertIn("=========Iteration: 1=========", formatted)
        self.assertIn("Thought: I need to search", formatted)
        self.assertIn("Action: search - Objective: Find papers", formatted)
        self.assertIn("Observation: Found papers", formatted)
        self.assertIn("Answer: Here are the results", formatted)
        self.assertIn("=========End of Iteration: 1=========", formatted)

    def test_format_conversation_history(self):
        """Test conversation history formatting."""
        self.state.add_conversation_entry("What is AI?", "AI is artificial intelligence")
        self.state.add_conversation_entry("Tell me more")

        formatted = self.state.format_conversation_history()

        self.assertIn("User: What is AI?", formatted)
        self.assertIn("Agent: AI is artificial intelligence", formatted)
        self.assertIn("User: Tell me more", formatted)

    def test_get_llm_output(self):
        """Test retrieving LLM output by iteration."""
        llm_output = LLMOutput(
            iteration=1,
            prompt="Test prompt",
            response="Test response",
            intent="search",
            follow_up_question=False
        )
        self.state.llm_outputs.append(llm_output)

        # Test existing iteration
        retrieved = self.state.get_llm_output(1)
        self.assertIsNotNone(retrieved)
        self.assertEqual(retrieved.prompt, "Test prompt")

        # Test non-existing iteration
        retrieved = self.state.get_llm_output(99)
        self.assertIsNone(retrieved)

    def test_get_recent_conversation_history(self):
        """Test getting recent conversation history with limits."""
        # Add more entries than the limit
        for i in range(6):
            self.state.add_conversation_entry(f"Message {i}", f"Response {i}")

        # Test default limit (4)
        recent = self.state.get_recent_conversation_history()
        self.assertEqual(len(recent), 4)
        self.assertEqual(recent[0].user, "Message 2")  # Should start from index 2
        self.assertEqual(recent[-1].user, "Message 5")  # Should end at last message

        # Test custom limit
        recent = self.state.get_recent_conversation_history(max_entries=2)
        self.assertEqual(len(recent), 2)
        self.assertEqual(recent[0].user, "Message 4")
        self.assertEqual(recent[1].user, "Message 5")

        # Test when fewer entries than limit
        short_state = AgentState(conversation_id="short", query="test")
        short_state.add_conversation_entry("Only message")
        recent = short_state.get_recent_conversation_history(max_entries=4)
        self.assertEqual(len(recent), 1)


class TestLLMOutput(unittest.TestCase):
    """Test LLMOutput model functionality."""

    def test_llm_output_to_dict(self):
        """Test LLMOutput conversion to dictionary."""
        # Create with parsed response
        agent_response = AgentResponse(
            intent="search",
            follow_up_question=False,
            thought="I need to search",
            action=ActionModel(name="search", reason="Find papers"),
            answer="Found results"
        )

        llm_output = LLMOutput(
            iteration=1,
            prompt="Test prompt",
            response="Test response",
            intent="search",
            follow_up_question=False,
            parsed_response=agent_response,
            thinking_time_seconds=1.5,
            action_time_seconds=2.3,
            input_tokens=100,
            output_tokens=50
        )

        result_dict = llm_output.to_dict()

        self.assertEqual(result_dict["iteration"], 1)
        self.assertEqual(result_dict["prompt"], "Test prompt")
        self.assertEqual(result_dict["intent"], "search")
        self.assertEqual(result_dict["thought"], "I need to search")
        self.assertEqual(result_dict["action"], agent_response.action)
        self.assertEqual(result_dict["answer"], "Found results")
        self.assertEqual(result_dict["thinking_time_seconds"], 1.5)
        self.assertEqual(result_dict["action_time_seconds"], 2.3)
        self.assertEqual(result_dict["input_tokens"], 100)
        self.assertEqual(result_dict["output_tokens"], 50)

    def test_llm_output_to_dict_no_parsed_response(self):
        """Test LLMOutput to_dict when parsing failed."""
        llm_output = LLMOutput(
            iteration=1,
            prompt="Test prompt",
            response="Invalid response",
            intent="unknown",
            follow_up_question=False,
            parsed_response=None
        )

        result_dict = llm_output.to_dict()

        self.assertEqual(result_dict["thought"], "Failed to parse response")
        self.assertIsNone(result_dict["action"])
        self.assertIsNone(result_dict["answer"])


if __name__ == "__main__":
    unittest.main()
