"""Command-line interface for running the agent with pipeline support."""

import argparse
import asyncio
import json
import logging
import sys
import time
import uuid
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, Optional

from rich.console import Console
from rich.markdown import Markdown
from rich.panel import Panel
from rich.prompt import Prompt
from rich.table import Table

from src.agent.config import AgentConfig
from src.agent.main import Agent

# Create logs directory if it doesn't exist
log_directory = Path("logs")
log_directory.mkdir(exist_ok=True)

# Create timestamped log file
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_file = log_directory / f"agent_cli_{timestamp}.log"


# Set up logging with proper configuration
def setup_logging(verbose: bool = False):
    """Set up logging configuration."""
    # Clear any existing handlers
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)

    # Create formatter
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # File handler - always log everything to file
    file_handler = logging.FileHandler(log_file, mode="w")
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)

    # Console handler - adjust level based on verbose flag
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO if verbose else logging.WARNING)
    console_handler.setFormatter(formatter)

    # Configure root logger
    logging.root.setLevel(logging.DEBUG)
    logging.root.addHandler(file_handler)
    logging.root.addHandler(console_handler)

    # Create specific logger for waiting messages
    waiting_logger = logging.getLogger("waiting_messages")
    waiting_logger.setLevel(logging.INFO)

    return waiting_logger


logger = logging.getLogger(__name__)
console = Console()


class AgentCLI:
    """Command-line interface for the research agent."""

    def __init__(self, config_path: Optional[str] = None, verbose: bool = False):
        """Initialize the CLI with configuration."""
        self.config_path = config_path
        self.verbose = verbose
        self.agent: Optional[Agent] = None
        self.conversation_id = str(uuid.uuid4())

        # Set up logging first
        self.waiting_logger = setup_logging(verbose)

        # Log the start of the session
        logger.info(
            f"Starting CLI session with conversation ID: {self.conversation_id}"
        )
        logger.info(f"Log file created: {log_file}")

        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file or use defaults."""
        if self.config_path and Path(self.config_path).exists():
            with open(self.config_path, "r") as f:
                config_data = json.load(f)
            console.print(
                f"[green]Loaded configuration from {self.config_path}[/green]"
            )
            logger.info(f"Loaded configuration from {self.config_path}")
        else:
            # Use default configuration from AgentConfig
            agent_config = AgentConfig()
            config_data = {
                "project_id": agent_config.project_id,
                "location": agent_config.location,
                "model_name": agent_config.model_name,
                "max_iterations": agent_config.max_iterations,
                "verbose": self.verbose,
                "temperature": agent_config.temperature,
                "max_tokens": agent_config.max_tokens,
            }
            console.print("[yellow]Using default configuration[/yellow]")
            logger.info("Using default configuration")

        # Override verbose setting from CLI
        config_data["verbose"] = self.verbose

        logger.info(f"Configuration loaded: {config_data}")
        return config_data

    async def initialize_agent(self) -> None:
        """Initialize the agent with the loaded configuration."""
        try:
            console.print("[blue]Initializing agent with pipeline support...[/blue]")
            logger.info("Starting agent initialization")

            self.agent = Agent(
                config=self.config,
                conversation_id=self.conversation_id,
            )

            console.print("[green]✅ Agent initialized successfully![/green]")
            logger.info("Agent initialized successfully")

            # Display agent configuration
            if self.verbose:
                self._display_agent_info()

        except Exception as e:
            console.print(f"[red]❌ Failed to initialize agent: {e}[/red]")
            logger.error(f"Agent initialization failed: {e}", exc_info=True)
            raise

    def _display_agent_info(self) -> None:
        """Display agent configuration and available pipelines."""
        logger.info("Displaying agent configuration")

        # Configuration table
        config_table = Table(title="Agent Configuration")
        config_table.add_column("Setting", style="cyan")
        config_table.add_column("Value", style="green")

        config_table.add_row("Conversation ID", self.conversation_id)
        config_table.add_row("Model", self.config.get("model_name", "Unknown"))
        config_table.add_row(
            "Max Iterations", str(self.config.get("max_iterations", "Unknown"))
        )
        config_table.add_row("Verbose", str(self.config.get("verbose", False)))
        config_table.add_row(
            "Temperature", str(self.config.get("temperature", "Unknown"))
        )
        config_table.add_row("Log File", str(log_file))

        console.print(config_table)
        console.print()

        # Available pipelines table
        if self.agent and hasattr(self.agent, "pipeline_manager"):
            pipelines_table = Table(title="Available Pipelines")
            pipelines_table.add_column("Intent", style="cyan")
            pipelines_table.add_column("Pipeline Name", style="green")
            pipelines_table.add_column("Description", style="yellow")

            for pipeline_info in self.agent.pipeline_manager.list_pipelines():
                pipelines_table.add_row(
                    pipeline_info.get("intent", "Unknown"),
                    pipeline_info.get("name", "Unknown"),
                    (
                        pipeline_info.get("description", "No description")[:60] + "..."
                        if len(pipeline_info.get("description", "")) > 60
                        else pipeline_info.get("description", "No description")
                    ),
                )

            console.print(pipelines_table)
            console.print()

    async def run_single_query(self, query: str) -> None:
        """Run a single query and display the result."""
        if not self.agent:
            await self.initialize_agent()

        try:
            console.print(Panel(f"[bold blue]Query:[/bold blue] {query}", expand=False))
            console.print("[yellow]🤔 Processing query...[/yellow]")

            logger.info(f"Processing single query: {query}")
            self.waiting_logger.info(f"Single query mode started: {query}")

            start_time = time.time()
            result = await self.agent.execute(query)
            end_time = time.time()

            # Display the intent that was identified
            if hasattr(self.agent, "state") and self.agent.state.llm_outputs:
                latest_output = self.agent.state.llm_outputs[-1]
                if latest_output.intent:
                    console.print(
                        f"[bold magenta]🎯 Intent:[/bold magenta] [bright_magenta]{latest_output.intent}[/bright_magenta]"
                    )
            # Display execution statistics
            execution_time = end_time - start_time
            console.print(f"[dim]⏱️  Execution time: {execution_time:.2f} seconds[/dim]")

            # Display result
            console.print()
            console.print(
                Panel(
                    Markdown(result),
                    title="[bold green]Agent Response[/bold green]",
                    expand=False,
                )
            )

            logger.info(f"Query completed in {execution_time:.2f} seconds")
            self.waiting_logger.info("Single query completed successfully")

            if (
                self.verbose
                and hasattr(self.agent, "state")
                and self.agent.state.llm_outputs
            ):
                self._display_execution_stats()

            # Display tool data including URLs
            if hasattr(self.agent, "tool_manager"):
                tool_data = self.agent.tool_manager.get_tool_data()
                if tool_data.get("data_used_url") or tool_data.get(
                    "url_post_processed"
                ):
                    url_table = Table(title="Data URLs Used")
                    url_table.add_column("Type", style="cyan")
                    url_table.add_column("URL", style="green")

                    if tool_data.get("data_used_url"):
                        url_table.add_row("Original Data", tool_data["data_used_url"])
                    if tool_data.get("url_post_processed"):
                        url_table.add_row(
                            "Processed Data", tool_data["url_post_processed"]
                        )

                    console.print(url_table)
                    console.print()

        except Exception as e:
            console.print(f"[red]❌ Error processing query: {e}[/red]")
            logger.error(f"Query execution failed: {e}", exc_info=True)

    async def run_interactive(self) -> None:
        """Run the agent in interactive mode."""
        if not self.agent:
            await self.initialize_agent()

        logger.info("Starting interactive mode")
        self.waiting_logger.info("Interactive mode started")

        console.print()
        console.print(
            Panel(
                "[bold green]🚀 Interactive Research Agent[/bold green]\n\n"
                "Ask questions about development economics, impact evaluation, and social programs.\n"
                "Type 'exit', 'quit', or 'bye' to end the conversation.\n"
                "Type 'help' for available commands.\n"
                "Type 'stats' to see execution statistics.\n"
                "Type 'clear' to clear conversation history.",
                title="Welcome",
                expand=False,
            )
        )
        console.print()

        conversation_count = 0

        while True:
            try:
                # Get user input
                query = Prompt.ask(
                    f"[bold cyan]Query #{conversation_count + 1}[/bold cyan]",
                    default="",
                ).strip()

                if not query:
                    continue

                logger.info(f"User input #{conversation_count + 1}: {query}")

                # Handle special commands
                if query.lower() in ["exit", "quit", "bye"]:
                    console.print("[yellow]👋 Goodbye![/yellow]")
                    logger.info("User requested exit")
                    break

                elif query.lower() == "help":
                    self._display_help()
                    logger.info("Help command executed")
                    continue

                elif query.lower() == "stats":
                    self._display_execution_stats()
                    logger.info("Stats command executed")
                    continue

                elif query.lower() == "clear":
                    if self.agent:
                        self.conversation_id = str(uuid.uuid4())
                        self.agent = Agent(
                            config=self.config,
                            conversation_id=self.conversation_id,
                        )
                    console.print("[green]✅ Conversation history cleared[/green]")
                    logger.info("Conversation history cleared")
                    conversation_count = 0
                    continue

                elif query.lower() == "config":
                    self._display_agent_info()
                    logger.info("Config command executed")
                    continue

                elif query.lower() == "urls":
                    self._display_tool_urls()
                    logger.info("URLs command executed")
                    continue

                # Process the query
                console.print("[yellow]🤔 Processing query...[/yellow]")

                start_time = time.time()
                result = await self.agent.execute(query)
                end_time = time.time()

                conversation_count += 1

                # Display the intent that was identified
                if hasattr(self.agent, "state") and self.agent.state.llm_outputs:
                    latest_output = self.agent.state.llm_outputs[-1]
                    if latest_output.intent:
                        console.print(
                            f"[bold magenta]🎯 Intent:[/bold magenta] [bright_magenta]{latest_output.intent}[/bright_magenta]"
                        )

                # Display result
                # Display brief stats
                execution_time = end_time - start_time
                console.print(
                    f"[dim]⏱️  Execution time: {execution_time:.2f} seconds[/dim]"
                )
                console.print()
                console.print(
                    Panel(
                        Markdown(result),
                        title=f"[bold green]Response #{conversation_count}[/bold green]",
                        expand=False,
                    )
                )

                logger.info(
                    f"Query #{conversation_count} completed in {execution_time:.2f} seconds"
                )

                # Display tool data including URLs
                if hasattr(self.agent, "tool_manager"):
                    tool_data = self.agent.tool_manager.get_tool_data()
                    if tool_data.get("data_used_url") or tool_data.get(
                        "url_post_processed"
                    ):
                        url_table = Table(title="Data URLs Used")
                        url_table.add_column("Type", style="cyan")
                        url_table.add_column("URL", style="green")

                        if tool_data.get("data_used_url"):
                            url_table.add_row(
                                "Original Data", tool_data["data_used_url"]
                            )
                        if tool_data.get("url_post_processed"):
                            url_table.add_row(
                                "Processed Data", tool_data["url_post_processed"]
                            )

                        console.print(url_table)
                        console.print()

            except KeyboardInterrupt:
                console.print("\n[yellow]👋 Goodbye![/yellow]")
                logger.info("Session interrupted by user")
                break
            except Exception as e:
                console.print(f"[red]❌ Error: {e}[/red]")
                logger.error(f"Interactive session error: {e}", exc_info=True)

    def _display_help(self) -> None:
        """Display help information."""
        help_text = f"""
[bold cyan]Available Commands:[/bold cyan]

• [green]help[/green] - Show this help message
• [green]stats[/green] - Display execution statistics for the current session
• [green]config[/green] - Show agent configuration and available pipelines
• [green]clear[/green] - Clear conversation history and start fresh
• [green]exit/quit/bye[/green] - Exit the interactive session

[bold cyan]Session Info:[/bold cyan]

• Conversation ID: {self.conversation_id}
• Log File: {log_file}

[bold cyan]Example Queries:[/bold cyan]

• "What are the effects of cash transfers on education?"
• "Compare the impact of microfinance vs. cash transfers"
• "How do we measure the impact of health interventions?"
• "What are the implementation challenges of conditional cash transfers?"

[bold cyan]Tips:[/bold cyan]

• Be specific in your questions for better results
• Ask follow-up questions to dive deeper into topics
• The agent maintains conversation context across queries
• All interactions are logged to the log file for debugging
"""
        console.print(Panel(help_text, title="Help", expand=False))

    def _display_execution_stats(self) -> None:
        """Display execution statistics."""
        if (
            not self.agent
            or not hasattr(self.agent, "state")
            or not self.agent.state.llm_outputs
        ):
            console.print("[yellow]No execution statistics available yet.[/yellow]")
            return

        stats_table = Table(title="Execution Statistics")
        stats_table.add_column("Iteration", style="cyan")
        stats_table.add_column("Intent", style="green")
        stats_table.add_column("Thinking Time (s)", style="yellow")
        stats_table.add_column("Action Time (s)", style="magenta")
        stats_table.add_column("Input Tokens", style="blue")
        stats_table.add_column("Output Tokens", style="red")

        total_thinking_time = 0
        total_action_time = 0
        total_input_tokens = 0
        total_output_tokens = 0

        for output in self.agent.state.llm_outputs:
            thinking_time = output.thinking_time_seconds or 0
            action_time = output.action_time_seconds or 0
            input_tokens = output.input_tokens or 0
            output_tokens = output.output_tokens or 0

            stats_table.add_row(
                str(output.iteration),
                output.intent or "Unknown",
                f"{thinking_time:.2f}",
                f"{action_time:.2f}" if action_time > 0 else "N/A",
                str(input_tokens),
                str(output_tokens),
            )

            total_thinking_time += thinking_time
            total_action_time += action_time
            total_input_tokens += input_tokens
            total_output_tokens += output_tokens

        # Add totals row
        stats_table.add_row(
            "[bold]TOTAL[/bold]",
            "",
            f"[bold]{total_thinking_time:.2f}[/bold]",
            f"[bold]{total_action_time:.2f}[/bold]",
            f"[bold]{total_input_tokens}[/bold]",
            f"[bold]{total_output_tokens}[/bold]",
        )

        console.print(stats_table)

        # Display additional stats
        console.print(
            f"[dim]Total conversations: {len(self.agent.state.conversation_history)}[/dim]"
        )
        console.print(
            f"[dim]Total history entries: {len(self.agent.state.history)}[/dim]"
        )
        console.print(f"[dim]Log file: {log_file}[/dim]")

        logger.info(
            f"Stats displayed - Total conversations: {len(self.agent.state.conversation_history)}, History entries: {len(self.agent.state.history)}"
        )

    async def cleanup(self) -> None:
        """Clean up resources."""
        logger.info("Starting cleanup process")
        if self.agent:
            await self.agent.cleanup()
            console.print("[green]✅ Agent cleanup completed[/green]")

        logger.info("CLI session completed")
        console.print(f"[dim]📄 Session logs saved to: {log_file}[/dim]")

    def _display_tool_urls(self) -> None:
        """Display current tool URLs."""
        if not self.agent or not hasattr(self.agent, "tool_manager"):
            console.print("[yellow]No agent or tool manager available[/yellow]")
            return

        tool_data = self.agent.tool_manager.get_tool_data()

        url_table = Table(title="Current Tool URLs")
        url_table.add_column("Type", style="cyan")
        url_table.add_column("URL", style="green")
        url_table.add_column("Status", style="yellow")

        urls = [
            ("Original Data", tool_data.get("data_used_url")),
            ("Processed Data", tool_data.get("url_post_processed")),
        ]

        for url_type, url in urls:
            if url:
                url_table.add_row(url_type, url, "✅ Available")
            else:
                url_table.add_row(url_type, "Not set", "❌ Not available")

        console.print(url_table)


async def main():
    """Main entry point for the CLI."""
    parser = argparse.ArgumentParser(
        description="Research Agent CLI with Pipeline Support",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
                Examples:
                # Interactive mode with verbose output
                python run_agent.py --interactive --verbose

                # Single query mode
                python run_agent.py --query "What are the effects of cash transfers?"

                # Use custom configuration
                python run_agent.py --config config.json --interactive

                Logs will be saved to: logs/agent_cli_{timestamp}.log
                        """,
    )

    parser.add_argument(
        "--config", "-c", type=str, help="Path to configuration JSON file"
    )

    parser.add_argument(
        "--query", "-q", type=str, help="Single query to execute (non-interactive mode)"
    )

    parser.add_argument(
        "--interactive",
        "-i",
        action="store_true",
        help="Run in interactive mode (default if no query provided)",
    )

    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="Enable verbose output and detailed logging",
    )

    args = parser.parse_args()

    # Initialize CLI
    cli = AgentCLI(config_path=args.config, verbose=args.verbose)

    try:
        if args.query:
            # Single query mode
            await cli.run_single_query(args.query)
        else:
            # Interactive mode (default)
            await cli.run_interactive()

    except KeyboardInterrupt:
        console.print("\n[yellow]Operation cancelled by user[/yellow]")
    except Exception as e:
        console.print(f"[red]Fatal error: {e}[/red]")
        logger.error(f"Fatal error in main: {e}", exc_info=True)
        return 1
    finally:
        await cli.cleanup()

    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
