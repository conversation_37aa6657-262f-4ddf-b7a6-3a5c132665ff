import React, { useState, useEffect, useRef } from "react";
import { Grid, Box, Typography } from "@mui/material";
import { Message, Source, PlotData } from "../../../types/ConversationTypes";
import Markdown from "markdown-to-jsx";
import StreamingSummary from "./Streaming/StreamingSummary";
import { replaceTags, formatSummaryText } from './Streaming/Utils';
import LinkComponent from "./Streaming/LinkComponent";
import './SummarySection.css';
import { NormalizeSpaces } from "./Streaming/Utils";
import AccessibleHeader from './Streaming/AnimatedMarkdown/AccessibleHeader';

interface SummarySectionProps {
  messageId: string;
  summary: Message;
  conversationId: string;
  informationMessageId: string;
  onViewOnPlotClicked: (payload: { citation_ids: { key: string; value: string }[]; messageId: string; plotDataInfo?: any }) => void;
  onViewOnSourceClicked: (payload: { paper_ids: string[]; messageId: string }) => void;
  setDisplaySystemLoader: (flag: boolean) => void;
  setStreamingEnded: (flag: boolean) => void;
  setSummaryStreamedText: (text: any) => void;
  setFullMessageInfoFetched: (message: Message) => void;
}

const SummarySection: React.FC<SummarySectionProps> = ({
  messageId,
  summary,
  conversationId,
  informationMessageId,
  onViewOnPlotClicked,
  onViewOnSourceClicked,
  setDisplaySystemLoader,
  setStreamingEnded,
  setSummaryStreamedText,
  setFullMessageInfoFetched
}) => {
  const [displayText, setDisplayText] = useState("");
  const [sources, setSources] = useState<Source[]>([]);
  const [plotData, setPlotData] = useState<PlotData[]>([]);
  const lastHeaderLevelRef = useRef(0);

  useEffect(() => {
    if (summary.text && summary.text.length > 0) {
      setDisplayText(formatSummaryText(summary.text));
    }
  }, [summary]);

  useEffect(() => {
    const updatedSources = summary?.sources ?? [];
    const updatedPlotData = summary?.plot?.data ?? [];
    setSources(updatedSources);
    setPlotData(updatedPlotData);
  }, [summary?.sources, summary?.plot?.data]);

  const headerLevels = ['h1', 'h2', 'h3', 'h4', 'h5'] as const;
  type HeaderLevel = typeof headerLevels[number];

  const renderMarkdownContent = (text: string) => (
    <>
      <Box sx={{ display: 'flex', alignItems: text.trim().length >= 80 ? "flex-start" : "center" }}>
        {text.trim().length > 0 && (
          <Box
            sx={{
              width: 32,
              height: 32,
              marginRight: 2,
              flexShrink: 0,
              backgroundImage: `url('/logo_icon.svg')`,
              backgroundSize: 'contain',
              backgroundRepeat: 'no-repeat',
            }}
          />
        )}
        <Markdown
          className="markdown-container"
          options={{
            overrides: {
              ...headerLevels.reduce((acc, level) => {
                acc[level as HeaderLevel] = {
                  component: (props: any) => (
                    <AccessibleHeader
                      as={level}
                      currentHeaderLevelRef={lastHeaderLevelRef}
                      {...props}
                    />
                  ),
                };
                return acc;
              }, {} as Record<HeaderLevel, any>),
              p: {
                component: 'div',
                props: {
                  component: 'div',
                },
              },
              a: {
                component: LinkComponent, props: {
                  messageId: messageId,
                  onViewOnPlotClicked: onViewOnPlotClicked,
                  onViewOnSourceClicked: onViewOnSourceClicked,
                  plotData: plotData
                }
              }
            }
          }}
        >
          {replaceTags(text, sources, plotData)}
        </Markdown>
      </Box>
    </>
  );

  return (
    <Grid container spacing={0} id={`/conversations/${conversationId}`} summary-trigger={messageId}>
      <Grid item xs={12}>
        <Box sx={{ p: 0, display: "flex", alignItems: displayText.length > 80 ? "flex-start" : "center" }}>
          <Box component="div" className="markdown-container" sx={{ flex: 1 }}>
            <Typography variant="body1" component="div" fontSize={16} sx={{ textAlign: "left", letterSpacing: "0.15px", lineHeight: "150%" }}>
              {summary.id && summary.id === 'system-loading' ? (
                <StreamingSummary
                  conversationId={conversationId}
                  informationMessageId={informationMessageId}
                  setDisplaySystemLoader={setDisplaySystemLoader}
                  setStreamingEnded={setStreamingEnded}
                  setSummaryStreamedText={setSummaryStreamedText}
                  onViewOnPlotClicked={onViewOnPlotClicked}
                  onViewOnSourceClicked={onViewOnSourceClicked}
                  messageId={messageId}
                  setDisplayText={setDisplayText}
                  setFullMessageInfoFetched={setFullMessageInfoFetched}
                />
              ) : (
                renderMarkdownContent(NormalizeSpaces(displayText))
              )}
            </Typography>
          </Box>
        </Box>
      </Grid>
    </Grid>
  );
};

export default SummarySection;