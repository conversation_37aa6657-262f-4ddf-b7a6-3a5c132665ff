import React, { useState, useEffect, useRef } from "react";
import {
  <PERSON>,
  Button,
  Collapse,
  ToggleButton,
  ToggleButtonGroup,
  Slider,
  Select,
  MenuItem,
} from "@mui/material";
import { FilterAlt } from "@mui/icons-material";
import { useTheme } from "@mui/material/styles";
import "./EffectSizesPlot.css";
import { NewForestPlotProps } from "./ForestPlotJWT.types";
import * as d3 from "d3";

const EffectSizesPlot = ({
  data,
  plotData,
  hoveredPair,
  outcomeHover,
  interventionHover,
  selectedOutcome,
  hoveredIntervention,
  selectedStudy,
  onStudyPointerOver,
  onStudyPointerOut,
}: NewForestPlotProps) => {
  const theme = useTheme();
  const containerRef = useRef<HTMLDivElement>(null);
  const [svgWidth, setSvgWidth] = useState(0);

  // const yearExtent = d3.extent(plotData, (d) => d.pulication_year);
  // const [qualityScore, setQualityScore] = useState<number[]>([0, 1]);
  // const [publicationYear, setPublicationYear] = useState<
  //   number[] | undefined[] | string[]
  // >(yearExtent);
  // const [region, setRegion] = useState<number[] | null>(null);
  // const [open, setOpen] = useState(false);
  // const [qualityScoreGroup, setQualityScoreGroup] = useState([
  //   "Low",
  //   "Medium",
  //   "High",
  // ]);

  const handlePublicationYearChange = (
    event: Event,
    newValue: number | number[]
  ) => {
    setPublicationYear(newValue as number[]);
  };

  const handleRegionChange = (event) => {
    setRegion(event.target.value);
  };

  const handleQualityScoreGroupChange = (
    event: React.MouseEvent<HTMLElement>,
    newQualityScoreGroup: string
  ) => {
    if (qualityScoreGroup.indexOf(newQualityScoreGroup) === -1) {
      setQualityScoreGroup((prev) => [...prev, newQualityScoreGroup]);
    } else {
      setQualityScoreGroup((prev) =>
        prev.filter((item) => item !== newQualityScoreGroup)
      );
    }
  };

  // console.log(selectedStudy);
  useEffect(() => {
    if (containerRef.current) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          setSvgWidth(entry.contentRect.width);
        }
      });
      resizeObserver.observe(containerRef.current);

      return () => resizeObserver.disconnect();
    }
  }, []);

  if (!data) {
    return null;
  }

  // const studies = data
  //   .map((item) =>
  //     item.data.map((d) => ({
  //       ...d,
  //       intervention_id: item.intervention_id,
  //       outcome_id: item.outcome_id,
  //     }))
  //   )
  //   .flat()
  //   .filter((d) => d.outcome_id === selectedOutcome.id)
  //   .sort((a, b) => b.score.value - a.score.value);
  console.log("SELECTEDOUTCOME", selectedOutcome);
  const selectedEffectSizes = data?.flat_effect_sizes?.filter(d => d.outcome_tag_ids === selectedOutcome.outcome_id)
  const studyIDs = Array.from(new Set(selectedEffectSizes.map(d => d.paper_id)));
  const studies = data?.studies?.filter(d => studyIDs.includes(d.id))
console.log("SES", selectedEffectSizes, selectedOutcome)
  const margin = {
    top: 40,
    right: 20,
    bottom: 0,
    left: 20,
  };

  const STUDY_HEIGHT = 16;

  // const height = 800;
  const height = studies.length * STUDY_HEIGHT + margin.top + margin.bottom;
  const width: number = containerRef.current?.offsetWidth || 0;
  const w: number = width - margin.left - margin.right;
  const h: number = height - margin.top - margin.bottom;

  const xScale = d3
    .scaleLinear()
    .domain([
      Math.min(
        0,
        d3.min(selectedEffectSizes, (d) => d.standardized_ci_lower)
      ),
      Math.max(
        0,
        d3.max(selectedEffectSizes, (d) => d.standardized_ci_upper)
      ),
    ])
    .range([0, w])
    .nice();

  const cScale = d3
    .scaleLinear()
    .domain([-0.8, -0.5, -0.2, 0, 0.2, 0.5, 0.8])
    .range([
      "#8aaf5f",
      "#afc183",
      "#c7d6ae",
      "#ffffff",
      "#a7c6e1",
      "#7aabd2",
      "#4e8fc0",
    ])
    .clamp(true);
  // .range(['#d87a8c', '#eca6a0', '#fad2c6', '#ffffff', '#6da7d1', '#5e9bc8', '#4e8fc0'])

  const meanEffectValue = d3.mean(
    selectedEffectSizes,
    (d) => d.cohen_d
  );
  // console.log("DATA", data);
  // console.log('00000', meanEffectValue)

  return (
    <Box
      className="funnel-plot-box"
      ref={containerRef}
      style={{ background: "rgba(245, 249, 254, 1)" }}
    >
      <div className="container">
        <svg width={svgWidth} height={height + margin.bottom + margin.top}>
          <defs>
            <linearGradient
              id={`gradient-${xScale.domain()[0]}-${xScale.domain()[1]}`}
              x1="0%"
              y1="0%"
              x2="100%"
              y2="0%"
            >
              {xScale.ticks(5).map((stop) => {
                return (
                  <stop
                    offset={`${
                      ((stop - xScale.domain()[0]) /
                        (xScale.domain()[1] - xScale.domain()[0])) *
                      100
                    }%`}
                    stop-color={`${cScale(stop)}`}
                  />
                );
              })}
            </linearGradient>
          </defs>
          <g transform={`translate(${margin.left}, ${margin.top})`}>
            <line
              x1={xScale.range()[0]}
              y1={h}
              x2={xScale.range()[1]}
              y2={h}
              stroke="rgb(22 54 97)"
            />
            <line
              x1={xScale(0)}
              y1={h}
              x2={xScale(0)}
              y2={-margin.top}
              // stroke="#ccc"
              stroke="black"
              strokeDasharray="2 2"
            />

            <text
              x={w / 2}
              y={h}
              dy={30}
              style={{
                fontSize: 12,
                textAnchor: "middle",
                fontWeight: "bold",
                fill: "rgb(22 54 97)",
              }}
            >
              effect size
            </text>
            {meanEffectValue !== undefined && (
              <g
                className="mean-effect"
                transform={`translate(${xScale(meanEffectValue)}, 0)`}
              >
                <text dy={-20}>mean</text>
                <line y1={-18} y2={h} />
              </g>
            )}
            {selectedEffectSizes.map((study, i) => (
              <g
                transform={`translate(0, ${i * STUDY_HEIGHT})`}
                className="study"
                opacity={
                  hoveredIntervention === undefined ||
                  hoveredIntervention === study.intervention_ids
                    ? 1
                    : 0.1
                }
                onPointerOver={() => onStudyPointerOver(study)}
                onPointerOut={() => onStudyPointerOut()}
              >
                <rect
                  width={w}
                  height={STUDY_HEIGHT}
                  y={-STUDY_HEIGHT / 2}
                  // fill="rgb(245 249 254)"
                  fill="transparent"
                  opacity={
                    (selectedStudy !== selectedStudy?.paper_id) ===
                    study.paper_id
                      ? 1
                      : 0
                  }
                />
                <line
                  x1={xScale(study.standardized_ci_lower)}
                  x2={xScale(study.standardized_ci_upper)}
                  stroke="rgb(22 54 97)"
                />
                <line
                  x1={xScale(study.standardized_ci_lower)}
                  y1={-2}
                  x2={xScale(study.standardized_ci_lower)}
                  y2={2}
                  stroke="rgb(22 54 97)"
                />
                <line
                  x1={xScale(study.standardized_ci_upper)}
                  y1={-2}
                  x2={xScale(study.standardized_ci_upper)}
                  y2={2}
                  stroke="rgb(22 54 97)"
                />
                <g>
                  <circle
                    className="outline"
                    cx={xScale(study.cohen_d)}
                    r={8}
                    fill={`${cScale(study.cohen_d)}`}
                    fillOpacity={0.7}
                    stroke="rgb(22 54 97)"
                  />
                  <circle
                    cx={xScale(study.cohen_d)}
                    r={3}
                    fill="rgb(22 54 97)"
                    opacity={0.8}
                  />
                </g>
              </g>
            ))}
            {xScale.ticks(5).map((tick) => (
              <text
                y={h}
                x={xScale(tick)}
                dominantBaseline="central"
                dy={10}
                textAnchor="middle"
                fontSize={10}
                fill="rgb(22 54 97)"
              >
                {tick}
              </text>
            ))}
          </g>
        </svg>
      </div>

      {/* <div>
        <Button
          onClick={() => setOpen(!open)}
          variant="contained"
          sx={{ mb: 2 }}
        >
          <FilterAlt fontSize="14" />
        </Button>
        <Collapse in={open} timeout="auto" unmountOnExit>
          <div>Quality score</div>
          <ToggleButtonGroup size="small" value={qualityScoreGroup}>
            <ToggleButton value="Low" onChange={handleQualityScoreGroupChange}>
              Low
            </ToggleButton>
            <ToggleButton
              value="Medium"
              onChange={handleQualityScoreGroupChange}
            >
              Medium
            </ToggleButton>
            <ToggleButton value="High" onChange={handleQualityScoreGroupChange}>
              High
            </ToggleButton>
          </ToggleButtonGroup>

          <div>
            <div>Publication year</div>
            <Slider
              aria-label="PublicationYear"
              value={publicationYear}
              onChange={handlePublicationYearChange}
              min={yearExtent[0]}
              max={yearExtent[1]}
              marks={true}
              step={1}
              size="small"
            />
            <div style={{ display: "flex", justifyContent: "space-between" }}>
              <div style={{ fontSize: "12px" }}>{publicationYear[0]}</div>
              <div style={{ fontSize: "12px" }}>{publicationYear[1]}</div>
            </div>
          </div>
          <div>
            <div>Region {region}</div>
            <Select
              labelId="dropdown-label"
              id="dropdown"
              value={region}
              onChange={handleRegionChange}
              label="Select an Option"
            >
              <MenuItem value={null}>Any region</MenuItem>
              {allRegions.map((d) => (
                <MenuItem value={d}>{d}</MenuItem>
              ))}
            </Select>
          </div>
        </Collapse>
      </div> */}
    </Box>
  );
};

export default EffectSizesPlot;
