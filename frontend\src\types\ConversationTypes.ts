export interface ConversationResponse {
    success: boolean;
    data: {
        messages: Message[];
    };
}

export interface ConversationNewMessagedResponse {
    success: boolean;
    data: {
        message_id: string;
        information_message_id: string;
    };
}

export interface Source {
    id: string;
    paper_id: string;
    short_paper_id: string;
    title: string;
    doi_url: string | null;
    citation: string;
    authors?: string;
    position?: number;
    journal_name?: string;
    abstract?: string;
    country?: string;
    region?: string;
    income_group?: string;
    quality_score?: number;
    quality_score_category?: string;
    sector?: string;
    intervention_name?: string;
    intervention_details?: string;
    outcome_name?: string;
    outcome_details?: string;
    volume?: string;
    issue?: string;
    pages?: string;
}

export interface DownloadLinks {
    csv?: string;
    json?: string;
}

export interface PlotData {
    type: string;
    data?: PlotDataInfo;
}

export interface Plot {
    title?: string;
    data?: PlotData;
    download_links?: DownloadLinks;
    activeMessageId?: string;
}

export interface Message {
    id: string;
    conversation_id: string | undefined;
    text?: string;
    type: 'information' | 'answer' | 'question';
    author: 'system' | 'user';
    created_at?: string;
    updated_at?: string;
    has_plot?: boolean;
    has_sources?: boolean;
    plot?: Plot;
    sources?: Source[];
    download_links?: DownloadLinks;
    choices?: string[];
}

export interface AggregateData {
    lower: number;
    upper: number;
    value: number;
}

export interface DataPoint {
    country: string;
    label: string;
    paper_citation: string;
    paper_id: number;
    paper_title: number;
    score: AggregateData;
}

export interface EffectSize {
    aggregate: AggregateData;
    data: DataPoint[];
    intervention: string;
    intervention_id: number;
    outcome: string;
    outcome_id: number;
}

export interface Study {
    country: string;
    id: number;
    label: string;
    population: string;
    pulication_year: number;
    quality_score: number;
    region: string;
}


export interface SuggestedTopicsChoices {
    type: 'suggested_topics';
    options: string[];
}

export interface RelatedTopicsChoices {
    type: 'related_topics';
    options: string[];
}

export interface Option {
    ref: string;
    label: string;
    value: string;
    description: string;
}

export interface SourcesResponse {
    success: boolean;
    data: {
        sources: Source[];
    };
}
export interface PlotScore {
    lower: number;
    upper: number;
    value: number;
}

export interface PlotDataPoint {
    label: string;
    score: PlotScore;
    title: string;
    paper_id: number;
}

export interface PlotOutcome {
    data: Record<string, PlotDataPoint>;
    outcome: string;
    aggregate: PlotScore;
    outcome_id: number;
    intervention: string;
    intervention_id: number;
}

export interface PlotDownloadLinks {
    csv: string;
    json: string;
}

export interface PlotResponse {
    success?: boolean;
    data: {
        id: string;
        title: string;
        data: PlotOutcome[];
        download_links: PlotDownloadLinks;
    };
}

export interface TopicDetails {
    id: number;
    type: string;
    count: number;
    label: string;
    level: string;
    request: string;
}

export interface ChoiceOption {
    ref: string;
    label: string;
    topic: TopicDetails;
    value: string;
    description: string;
}

export interface FollowUpResponse {
    success: boolean;
    data: {
        id: string;
        conversation_id: string;
        text: string;
        author: string;
        type: string;
        choices?: string[];
        created_at: string;
        updated_at: string;
    };
}

export interface DataItem {
    label?: string;
    score?: PlotScore;
    country?: string;
    paper_id?: number;
    paper_title?: string;
    paper_citation?: string;
}

export interface TagEntry {
    id: string;
    label: string;
    value: string;
}

export interface TagsApiResponse {
    success: boolean;
    data: {
        entries: TagEntry[];
    };
}

export interface FlatEffectSize {
    paper_id: string;
    paper_combined_id: string;
    title: string;
    year: number;
    doi_url: string | null;
    doi: string | null;
    authors: string;
    first_author: string;
    journal_name: string | null;
    country_code: string;
    country_name: string;
    region: string;
    income_group: string;
    quality_score: number;
    quality_score_category: string;
    treatment_arm: string;
    intervention_id: string;
    intervention_tag_ids: string;
    intervention_tags_with_levels: Array<{ tag_label: string; level: number }>;
    outcome_tags_with_levels: Array<{ tag_label: string; level: number }>;
    intervention_tag_labels: string;
    intervention_tag_short_labels: string;
    intervention_tag_definitions: string | null;
    intervention_target_populations: string;
    intervention_sectors: string;
    intervention_objective: string;
    intervention_scale: string;
    intervention_intensity: string;
    intervention_fidelity: string;
    intervention_description: string;
    intervention_analysis_unit: string;
    intervention_cost: number | null;
    outcome_ids: string;
    outcome_tag_ids: string;
    outcome_tag_labels: string;
    outcome_tag_short_labels: string;
    outcome_tag_definition: string | null;
    outcome_target_populations: string;
    outcome_sectors: string;
    outcome_description: string;
    outcome_analysis_unit: string;
    outcome_connotation: string;
    outcome_type: string;
    is_primary_period: number;
    data_collection_round: string;
    cohen_d: number;
    hedges_d: number;
    standardized_ci_lower: number;
    standardized_ci_upper: number;
}

export interface PlotDataInfo {
    default_tab: string;
    studies: any[];
    interventions: any[];
    outcomes: any[];
    flat_effect_sizes: FlatEffectSize[];
    effect_sizes: any[];
}