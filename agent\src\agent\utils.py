"""Utility functions for the agent."""

import json
import logging
import sys
import time
from dataclasses import dataclass, field
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from jinja2 import Environment, FileSystemLoader

logger = logging.getLogger(__name__)

# Get the absolute path to the config directory
ROOT_DIR = Path(__file__).parent.parent.parent
PROMPTS_DIR = ROOT_DIR / "config" / "prompts"
SQL_DIR = ROOT_DIR / "config" / "sql"

# Initialize Jinja2 environment
env = Environment(
    loader=FileSystemLoader(str(PROMPTS_DIR)),
    trim_blocks=True,
    lstrip_blocks=True,
)


class AgentLogger:
    """Centralized logging for the agent with multiple output streams."""

    def __init__(self, conversation_id: str, verbose: bool = False):
        """Initialize the agent logger."""
        self.conversation_id = conversation_id
        self.verbose = verbose

        # Create logs directory
        self.log_directory = Path("logs")
        self.log_directory.mkdir(exist_ok=True)

        # Create timestamped log files
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.agent_log_file = self.log_directory / f"agent_cli_{timestamp}_actions.log"
        self.general_log_file = self.log_directory / f"agent_cli_{timestamp}.log"

        # Initialize loggers
        self._setup_waiting_logger()
        self._setup_general_logger()

    def _setup_waiting_logger(self):
        """Set up the waiting/action logger."""
        self.waiting_logger = logging.getLogger("waiting_messages")

        # Clear existing handlers
        for handler in self.waiting_logger.handlers[:]:
            self.waiting_logger.removeHandler(handler)

        # Create file handler for waiting messages
        waiting_handler = logging.FileHandler(self.agent_log_file)
        waiting_formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        waiting_handler.setFormatter(waiting_formatter)

        self.waiting_logger.addHandler(waiting_handler)
        self.waiting_logger.setLevel(logging.INFO)
        self.waiting_logger.propagate = False

    def _setup_general_logger(self):
        """Set up the general logger."""
        # Clear any existing handlers from root logger
        for handler in logging.root.handlers[:]:
            logging.root.removeHandler(handler)

        # Create formatter
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )

        # File handler - always log everything to file
        file_handler = logging.FileHandler(self.general_log_file, mode="w")
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)

        # Console handler - adjust level based on verbose flag
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO if self.verbose else logging.WARNING)
        console_handler.setFormatter(formatter)

        # Configure root logger
        logging.root.setLevel(logging.DEBUG)
        logging.root.addHandler(file_handler)
        logging.root.addHandler(console_handler)

    def log_session_start(self):
        """Log the start of a new session."""
        logger.info(f"Starting session with conversation ID: {self.conversation_id}")
        logger.info(f"Agent log file created: {self.agent_log_file}")
        logger.info(f"General log file created: {self.general_log_file}")

    def log_query_start(self, query: str):
        """Log the start of query processing."""
        logger.info(f"Processing query: {query}")
        self.waiting_logger.info(f"Starting to process query: {query}")

    def log_query_reformulated(self, original: str, reformulated: str):
        """Log query reformulation."""
        logger.info(f"Query reformulated from '{original}' to '{reformulated}'")

    def log_iteration_start(self, iteration: int, max_iterations: int):
        """Log the start of an iteration."""
        logger.info(f"Starting iteration {iteration}/{max_iterations}")
        self.waiting_logger.info(f"ITERATION {iteration}/{max_iterations}")

    def log_thinking_start(self, iteration: int):
        """Log the start of LLM thinking."""
        logger.info(f"Generating agent response for iteration {iteration}")
        logger.info("Building agent prompt with history and available pipelines")
        logger.info(f"Sending prompt to LLM for iteration {iteration}")
        self.waiting_logger.info(
            f"THINKING: Generating response for iteration {iteration}"
        )

    def log_thinking_complete(self, iteration: int, thinking_time: float):
        """Log completion of LLM thinking."""
        logger.info(f"LLM response received in {thinking_time:.2f} seconds")
        logger.info(f"Agent thought process complete for iteration {iteration}")
        self.waiting_logger.info(
            f"RESPONSE_RECEIVED: LLM responded in {thinking_time:.2f} seconds"
        )

    def log_thought(self, thought: str):
        """Log the agent's thought process."""
        thought_summary = thought[:150] + "..." if len(thought) > 150 else thought
        self.waiting_logger.info(f"THOUGHT: {thought_summary}")

    def log_agent_decision(
        self, action: Optional[Dict[str, Any]] = None, answer: Optional[str] = None
    ):
        """Log the agent's decision."""
        if action:
            pipeline_name = action.get("name", "Unknown")
            reason = action.get("reason", "No reason provided")

            logger.info(f"Agent decided to use pipeline: {pipeline_name}")
            logger.info(f"Pipeline usage reason: {reason}")
            self.waiting_logger.info(f"DECIDED: Use pipeline {pipeline_name}")
            self.waiting_logger.info(f"REASON: {reason}")

        elif answer:
            logger.info("Agent generated final answer")
            answer_length = len(answer)
            logger.info(f"Answer length: {answer_length} characters")
            self.waiting_logger.info(
                f"DECISION: Generate final answer of {answer_length} characters"
            )

    def log_pipeline_start(self, pipeline_name: str, pipeline_input: Dict[str, Any]):
        """Log the start of pipeline execution."""
        logger.info(f"Starting execution of pipeline: {pipeline_name}")
        logger.info(f"Pipeline input parameters: {list(pipeline_input.keys())}")
        self.waiting_logger.info(f"ACTION: Executing pipeline {pipeline_name}")
        self.waiting_logger.info(f"PIPELINE_PARAMS: {', '.join(pipeline_input.keys())}")

    def log_pipeline_complete(self, pipeline_name: str, execution_time: float):
        """Log completion of pipeline execution."""
        logger.info(
            f"Pipeline {pipeline_name} execution completed in {execution_time:.2f} seconds"
        )
        self.waiting_logger.info(
            f"PIPELINE_COMPLETE: {pipeline_name} completed in {execution_time:.2f} seconds"
        )

    def log_pipeline_error(self, pipeline_name: str, error: Exception):
        """Log pipeline execution error."""
        logger.error(f"Error executing pipeline {pipeline_name}: {error}")
        self.waiting_logger.error(f"PIPELINE_ERROR: {pipeline_name} failed with error")

    def log_final_answer(self, answer: str):
        """Log final answer generation."""
        logger.info("Final answer generated, completing execution")
        self.waiting_logger.info("FINAL ANSWER: Generated final response")

    def log_max_iterations_reached(self):
        """Log when max iterations are reached."""
        logger.info("Maximum iterations reached, terminating execution")
        self.waiting_logger.info(
            "MAX_ITERATIONS: Reached maximum iterations without final answer"
        )

    def log_parsing_error(self, iteration: int, error: Exception):
        """Log LLM response parsing error."""
        logger.error(f"Error parsing LLM response: {error}")
        self.waiting_logger.error(
            f"PARSE_ERROR: Could not parse LLM response in iteration {iteration}"
        )

    def log_cleanup_start(self):
        """Log the start of cleanup process."""
        logger.info("Starting agent cleanup")
        self.waiting_logger.info("CLEANUP: Starting agent cleanup process")

    def log_cleanup_complete(self):
        """Log completion of cleanup process."""
        logger.info("Agent cleanup complete")
        self.waiting_logger.info("CLEANUP: Agent cleanup complete")

    def log_session_close(self, session_type: str):
        """Log session closure."""
        logger.info(f"Closing {session_type} session")

    def log_query_reformulation_error(self, error: Exception):
        """Log query reformulation error."""
        logger.error(f"Error reformulating query: {error}")

    def log_no_response_warning(self, iteration: int):
        """Log warning when no agent response is received."""
        logger.warning(f"No agent response received in iteration {iteration}")

    def log_final_answer_rephrase_error(self, error: Exception):
        """Log final answer rephrasing errors."""
        self.waiting_logger.error(f"Error rephrasing final answer: {error}")

    def log_answer_rephrasing_start(self):
        """Log when answer rephrasing starts."""
        self.waiting_logger.info("Starting answer rephrasing for conversation flow")

    def log_answer_rephrasing_complete(
        self, original_length: int, rephrased_length: int
    ):
        """Log when answer rephrasing completes."""
        self.waiting_logger.info(
            f"Answer rephrasing complete - Original: {original_length} chars, Rephrased: {rephrased_length} chars"
        )

    @property
    def log_files(self) -> Dict[str, Path]:
        """Get the paths to log files."""
        return {"agent_actions": self.agent_log_file, "general": self.general_log_file}


class ExecutionStats:
    """Track execution statistics for the agent."""

    def __init__(self):
        """Initialize execution stats."""
        self.stats = {
            "total_thinking_time": 0.0,
            "total_action_time": 0.0,
            "total_iterations": 0,
            "successful_queries": 0,
            "failed_queries": 0,
            "pipeline_executions": 0,
        }

    def add_thinking_time(self, time_seconds: float):
        """Add thinking time to stats."""
        self.stats["total_thinking_time"] += time_seconds

    def add_action_time(self, time_seconds: float):
        """Add action time to stats."""
        self.stats["total_action_time"] += time_seconds

    def increment_iterations(self):
        """Increment iteration count."""
        self.stats["total_iterations"] += 1

    def mark_query_successful(self):
        """Mark a query as successful."""
        self.stats["successful_queries"] += 1

    def mark_query_failed(self):
        """Mark a query as failed."""
        self.stats["failed_queries"] += 1

    def increment_pipeline_executions(self):
        """Increment pipeline execution count."""
        self.stats["pipeline_executions"] += 1

    def get_stats(self) -> Dict[str, Any]:
        """Get current statistics."""
        return self.stats.copy()


def setup_logging(debug: bool = False) -> None:
    """Set up logging configuration."""
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    )


def load_prompt(
    template_name: str,
    query: str,
    history: str,
    conversation_history: str,
    pipelines: List[Dict[str, Any]],
    no_data_found: bool = False,
    extended_entities: str = "",
) -> str:
    """
    Load and render a prompt template with the given variables.

    Args:
        template_name: Name of the prompt template file (without extension)
        query: Current user query
        history: Current reasoning steps
        conversation_history: String containing the full conversation history
        pipelines: Available pipelines
        no_data_found: Boolean indicating if no data was found
        extended_entities: String containing the extended entities

    Returns:
        String containing the rendered prompt
    """
    try:
        # Append .prompt if not already present
        if not template_name.endswith(".prompt"):
            template_name = f"{template_name}.prompt"

        template = env.get_template(template_name)

        rendered_prompt = template.render(
            query=query,
            history=history,
            conversation_history=conversation_history,
            pipelines=pipelines,
            no_data_found=no_data_found,
            extended_entities=extended_entities,
        )
        return rendered_prompt
    except Exception as e:
        logger.error(f"Error loading prompt {template_name}: {e}")
        # Return a basic prompt if template loading fails
        return f"Please answer this query: {query}"


def load_schema() -> Dict[str, Any]:
    """Load the database schema from the schema file."""
    schema_path = ROOT_DIR / "data" / "schema.json"
    try:
        return json.loads(schema_path.read_text())
    except FileNotFoundError:
        logger.error("Schema file not found")
        raise


@dataclass
class StepMetrics:
    """Metrics for a single pipeline step."""

    step_name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    input_data: Optional[Dict[str, Any]] = None
    output_data: Optional[Dict[str, Any]] = None

    def mark_completed(self, success: bool = True, error_message: str = None):
        """Mark the step as completed."""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.success = success
        self.error_message = error_message

    def to_dict(self, include_full_data: bool = True) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        result = {
            "step_name": self.step_name,
            "duration": self.duration,
            "success": self.success,
            "error_message": self.error_message,
            "timestamp": (
                datetime.fromtimestamp(self.start_time).isoformat()
                if self.start_time
                else None
            ),
        }

        if include_full_data:
            # Include full input/output data
            result.update(
                {
                    "input_data": self._serialize_data(self.input_data),
                    "output_data": self._serialize_data(self.output_data),
                    "input_keys": (
                        list(self.input_data.keys()) if self.input_data else []
                    ),
                    "output_keys": (
                        list(self.output_data.keys()) if self.output_data else []
                    ),
                }
            )
        else:
            # Only include keys (for backward compatibility)
            result.update(
                {
                    "input_keys": (
                        list(self.input_data.keys()) if self.input_data else []
                    ),
                    "output_keys": (
                        list(self.output_data.keys()) if self.output_data else []
                    ),
                }
            )

        return result

    def _serialize_data(
        self, data: Optional[Dict[str, Any]]
    ) -> Optional[Dict[str, Any]]:
        """Serialize data for JSON storage, handling complex objects."""
        if not data:
            return None

        serialized = {}
        for key, value in data.items():
            try:
                # Try to serialize the value
                if isinstance(value, (str, int, float, bool, type(None))):
                    serialized[key] = value
                elif isinstance(value, (list, dict)):
                    # For lists and dicts, try to serialize as JSON
                    json.dumps(value)  # Test if it's JSON serializable
                    serialized[key] = value
                else:
                    # For complex objects, convert to string representation
                    serialized[key] = str(value)
            except (TypeError, ValueError):
                # If serialization fails, store as string representation
                serialized[key] = f"<{type(value).__name__}: {str(value)[:100]}>"

        return serialized


@dataclass
class PipelineMetrics:
    """Metrics for an entire pipeline execution."""

    pipeline_name: str
    intent: str
    user_query: str
    start_time: float
    end_time: Optional[float] = None
    total_duration: Optional[float] = None
    success: bool = True
    steps: List[StepMetrics] = field(default_factory=list)
    final_result: Optional[Dict[str, Any]] = None

    def mark_completed(self, success: bool = True, final_result: Dict[str, Any] = None):
        """Mark the pipeline as completed."""
        self.end_time = time.time()
        self.total_duration = self.end_time - self.start_time
        self.success = success
        self.final_result = final_result

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "pipeline_name": self.pipeline_name,
            "intent": self.intent,
            "user_query": self.user_query,
            "total_duration": self.total_duration,
            "success": self.success,
            "num_steps": len(self.steps),
            "failed_steps": len([s for s in self.steps if not s.success]),
            "steps": [step.to_dict() for step in self.steps],
            "timestamp": datetime.fromtimestamp(self.start_time).isoformat(),
            "final_result_keys": (
                list(self.final_result.keys()) if self.final_result else []
            ),
        }


class PipelineTracker:
    """Track pipeline execution metrics and save them."""

    def __init__(self, conversation_id: str):
        """Initialize the pipeline tracker."""
        self.conversation_id = conversation_id
        self.current_pipeline: Optional[PipelineMetrics] = None
        self.current_step: Optional[StepMetrics] = None
        self.completed_pipelines: List[PipelineMetrics] = []

        # Create metrics directory
        self.metrics_directory = Path("logs/metrics")
        self.metrics_directory.mkdir(parents=True, exist_ok=True)

        # Create timestamped metrics file
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.metrics_file = (
            self.metrics_directory
            / f"pipeline_metrics_{timestamp}_{conversation_id}.json"
        )

    def start_pipeline(self, pipeline_name: str, intent: str, user_query: str):
        """Start tracking a new pipeline."""
        self.current_pipeline = PipelineMetrics(
            pipeline_name=pipeline_name,
            intent=intent,
            user_query=user_query,
            start_time=time.time(),
        )
        logger.info(f"Started tracking pipeline: {pipeline_name}")

    def start_step(self, step_name: str, input_data: Dict[str, Any] = None):
        """Start tracking a pipeline step with full input data."""
        if not self.current_pipeline:
            logger.warning("No active pipeline to track step for")
            return

        # Ensure we capture the full input data, not just keys
        full_input_data = input_data.copy() if input_data else {}

        self.current_step = StepMetrics(
            step_name=step_name, start_time=time.time(), input_data=full_input_data
        )
        logger.debug(
            f"Started tracking step: {step_name} with inputs: {list(full_input_data.keys())}"
        )

    def end_step(
        self,
        success: bool = True,
        error_message: str = None,
        output_data: Dict[str, Any] = None,
    ):
        """End tracking of current step with full output data."""
        if not self.current_step:
            logger.warning("No active step to end")
            return

        # Ensure we capture the full output data, not just keys
        full_output_data = output_data.copy() if output_data else {}
        self.current_step.output_data = full_output_data
        self.current_step.mark_completed(success, error_message)

        if self.current_pipeline:
            self.current_pipeline.steps.append(self.current_step)

        logger.debug(
            f"Completed step: {self.current_step.step_name} (success: {success}, "
            f"duration: {self.current_step.duration:.2f}s, "
            f"outputs: {list(full_output_data.keys())})"
        )
        self.current_step = None

    def end_pipeline(self, success: bool = True, final_result: Dict[str, Any] = None):
        """End tracking of current pipeline."""
        if not self.current_pipeline:
            logger.warning("No active pipeline to end")
            return

        self.current_pipeline.mark_completed(success, final_result)
        self.completed_pipelines.append(self.current_pipeline)

        logger.info(
            f"Completed pipeline: {self.current_pipeline.pipeline_name} "
            f"(success: {success}, duration: {self.current_pipeline.total_duration:.2f}s)"
        )

        # Save metrics immediately
        self.save_metrics()
        self.current_pipeline = None

    def save_metrics(self, include_full_data: bool = True):
        """Save all metrics to file with option to include full data."""
        try:
            metrics_data = {
                "conversation_id": self.conversation_id,
                "total_pipelines": len(self.completed_pipelines),
                "successful_pipelines": len(
                    [p for p in self.completed_pipelines if p.success]
                ),
                "failed_pipelines": len(
                    [p for p in self.completed_pipelines if not p.success]
                ),
                "generated_at": datetime.now().isoformat(),
                "pipelines": [
                    self._pipeline_to_dict(pipeline, include_full_data)
                    for pipeline in self.completed_pipelines
                ],
            }

            with open(self.metrics_file, "w") as f:
                json.dump(metrics_data, f, indent=2)

            logger.info(f"Metrics saved to: {self.metrics_file}")

        except Exception as e:
            logger.error(f"Error saving metrics: {e}")

    def _pipeline_to_dict(
        self, pipeline: PipelineMetrics, include_full_data: bool = True
    ) -> Dict[str, Any]:
        """Convert pipeline metrics to dictionary with optional full data."""
        return {
            "pipeline_name": pipeline.pipeline_name,
            "intent": pipeline.intent,
            "user_query": pipeline.user_query,
            "total_duration": pipeline.total_duration,
            "success": pipeline.success,
            "num_steps": len(pipeline.steps),
            "failed_steps": len([s for s in pipeline.steps if not s.success]),
            "steps": [step.to_dict(include_full_data) for step in pipeline.steps],
            "timestamp": datetime.fromtimestamp(pipeline.start_time).isoformat(),
            "final_result_keys": (
                list(pipeline.final_result.keys()) if pipeline.final_result else []
            ),
            "final_result": pipeline.final_result if include_full_data else None,
        }

    def get_summary(self, include_full_data: bool = False) -> Dict[str, Any]:
        """Get a summary of all tracked metrics."""
        if not self.completed_pipelines:
            return {"message": "No pipelines tracked yet"}

        total_duration = sum(
            p.total_duration for p in self.completed_pipelines if p.total_duration
        )
        successful_pipelines = [p for p in self.completed_pipelines if p.success]
        failed_pipelines = [p for p in self.completed_pipelines if not p.success]

        return {
            "total_pipelines": len(self.completed_pipelines),
            "successful_pipelines": len(successful_pipelines),
            "failed_pipelines": len(failed_pipelines),
            "total_execution_time": total_duration,
            "average_pipeline_time": (
                total_duration / len(self.completed_pipelines)
                if self.completed_pipelines
                else 0
            ),
            "metrics_file": str(self.metrics_file),
            "pipelines": (
                [
                    self._pipeline_to_dict(pipeline, include_full_data)
                    for pipeline in self.completed_pipelines
                ]
                if include_full_data
                else None
            ),
        }
