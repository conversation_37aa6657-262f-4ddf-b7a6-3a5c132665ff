"""Main agent module implementing ReAct methodology with pipelines."""

import logging
import time
from typing import Any, Dict, Optional

import aiohttp

from src.agent.config import AgentConfig
from src.agent.models import (
    AgentCache,
    AgentResponse,
    AgentState,
    LLMOutput,
)
from src.agent.utils import Agent<PERSON>ogger, ExecutionStats, load_prompt
from src.pipelines.manager import PipelineManager
from src.tools.llm_client import LLMClient
from src.tools.manager import ToolManager

# Regular console logger for all output
logger = logging.getLogger(__name__)


class Agent:
    """ReAct agent for research queries using pipelines."""

    _current_instance = None

    def __init__(
        self,
        config: Dict[str, Any],
        conversation_id: Optional[str] = None,
    ):
        """Initialize the agent with configuration."""
        Agent._current_instance = self
        self.config = AgentConfig.from_dict(config)
        self.conversation_id = conversation_id or "default"

        # Initialize logging system
        self.agent_logger = AgentLogger(
            conversation_id=self.conversation_id, verbose=self.config.verbose
        )
        self.stats = ExecutionStats()

        self.agent_logger.log_session_start()

        # Check if a shared session was provided
        self.session = config.get("shared_session")
        self.owns_session = False

        # Create a new session only if one wasn't provided
        if self.session is None:
            self.session = aiohttp.ClientSession()
            self.owns_session = True

        # Initialize LLM client with new config (remove project_id and location)
        self.llm = LLMClient(
            model_name=self.config.model_name,
        )
        logger.info(f"LLM client initialized with model: {self.config.model_name}")

        # Initialize pipeline manager
        self.tool_manager = ToolManager(config=config, session=self.session)
        pipeline_config = config if isinstance(config, dict) else self.config.dict()
        self.pipeline_manager = PipelineManager(
            self.tool_manager, config=pipeline_config, session=self.session
        )

        # Log available pipelines through the agent logger
        self.agent_logger.waiting_logger.info(
            f"Available pipelines: {', '.join(self.pipeline_manager.list_intents())}"
        )

        logger.info(
            f"Pipeline manager initialized with {len(self.pipeline_manager.pipelines)} pipelines"
        )

        self.verbose = self.config.verbose
        self.max_iterations = self.config.max_iterations

        # Initialize agent cache (instance-specific to prevent data sharing)
        self.agent_cache = AgentCache()

        # Initialize agent state
        self.state = AgentState(
            conversation_id=self.conversation_id,
            max_iterations=self.max_iterations,
            query="",
            history=self.agent_cache.get_or_init_history(self.conversation_id),
            conversation_history=self.agent_cache.get_or_init_conversation_history(
                self.conversation_id
            ),
        )

        logger.info("Agent initialization complete")

    @classmethod
    def get_current_agent(cls) -> Optional["Agent"]:
        """Get the current agent instance."""
        return cls._current_instance

    async def _should_rephrase_for_conversation(self) -> bool:
        """Check if we should rephrase the answer for conversation flow."""
        # Only rephrase if there's previous conversation history with agent responses
        conversation_count = len(self.state.conversation_history)

        if self.verbose:
            await self._log_info(
                f"Checking rephrasing: conversation_history length = {conversation_count}"
            )

        if conversation_count <= 1:
            if self.verbose:
                await self._log_info("No rephrasing needed: conversation_history <= 1")
            return False

        # Check if any previous conversation entries have agent responses
        has_previous_agent_responses = False
        for entry in self.state.conversation_history[:-1]:
            if entry.agent:
                has_previous_agent_responses = True
                break

        if self.verbose:
            await self._log_info(
                f"Previous agent responses found: {has_previous_agent_responses}"
            )

        return has_previous_agent_responses

    def _sync_state_with_cache(self) -> None:
        """Sync agent state with instance cache."""
        self.agent_cache.update_history(self.conversation_id, self.state.history)
        self.agent_cache.update_conversation_history(
            self.conversation_id, self.state.conversation_history
        )

    async def execute(self, query: str) -> str:
        """Execute the agent's reasoning and action loop with pipelines."""
        await self._log_info(f"Starting agent execution for query: {query}")
        self.agent_logger.log_query_start(query)

        # Update state with current query
        self.state.query = query
        self.state.add_conversation_entry(query)
        self._sync_state_with_cache()

        # If this is a follow-up, reformulate the query
        if len(self.state.conversation_history) > 1:
            reformulated_query = await self._reformulate_query(query)
            self.agent_logger.log_query_reformulated(query, reformulated_query)
            current_query = reformulated_query
        else:
            current_query = query

        # Store the current query for pipeline execution
        self.state.current_query = current_query

        # Execute agent reasoning loop
        iteration = 0
        while iteration < self.max_iterations:
            iteration += 1
            self.state.current_iteration = iteration
            self.stats.increment_iterations()

            if self.verbose:
                await self._log_info(f"\n=========== Iteration {iteration} ===========")

            self.agent_logger.log_iteration_start(iteration, self.max_iterations)

            # Get agent response
            agent_response = await self._get_agent_response(current_query, iteration)
            if not agent_response:
                await self._log_info(
                    "No agent response received, continuing to next iteration"
                )
                self.agent_logger.log_no_response_warning(iteration)
                continue

            # Log LLM's thought process and action plan
            await self._log_info(f"LLM Thought: {agent_response.thought}")
            self.agent_logger.log_thought(agent_response.thought)

            # Handle final answer from agent response
            if agent_response.answer:
                await self._log_info(f"Final Answer: {agent_response.answer}")
                self.agent_logger.log_final_answer(agent_response.answer)

                self.state.add_to_history(
                    intent=agent_response.intent,
                    follow_up_question=agent_response.follow_up_question,
                    thought=agent_response.thought,
                    answer=agent_response.answer,
                    iteration=iteration,
                )
                self.state.update_last_conversation_entry(agent_response.answer)
                self._sync_state_with_cache()

                self.stats.mark_query_successful()
                return agent_response.answer

            # Execute pipeline if action is present
            if agent_response.action:
                result = await self._execute_pipeline_action(
                    agent_response, iteration, current_query
                )

                await self._log_info(f"Pipeline Result: {result}")
                self.agent_logger.log_pipeline_complete(
                    result, self.state.current_iteration
                )

                if result and result.get("answer"):
                    # Pipeline returned a final answer
                    final_answer = result["answer"]

                    if self.verbose:
                        await self._log_info(
                            f"Pipeline returned final answer: {len(final_answer)} characters"
                        )

                    # Check if we should rephrase for conversation flow
                    should_rephrase = await self._should_rephrase_for_conversation()
                    if should_rephrase:
                        if self.verbose:
                            await self._log_info(
                                "Rephrasing answer for conversation flow..."
                            )
                        final_answer = (
                            await self._rephrase_final_answer_for_conversation(
                                final_answer
                            )
                        )
                        if self.verbose:
                            await self._log_info("Answer rephrasing completed")
                    else:
                        if self.verbose:
                            await self._log_info(
                                "No rephrasing needed - using original answer"
                            )

                    self.state.update_last_conversation_entry(final_answer)
                    self.state.add_to_history(
                        intent=agent_response.intent,
                        follow_up_question=agent_response.follow_up_question,
                        thought=agent_response.thought,
                        answer=final_answer,
                        iteration=iteration,
                    )
                    self._sync_state_with_cache()
                    self.stats.mark_query_successful()
                    return final_answer

                # Add to history for continued reasoning
                self.state.add_to_history(
                    intent=agent_response.intent,
                    follow_up_question=agent_response.follow_up_question,
                    thought=agent_response.thought,
                    action=agent_response.action,
                    observation=str(result) if result else "No result",
                    iteration=iteration,
                )
                self._sync_state_with_cache()

                await self._log_info(
                    f"# Conversation History: {len(self.state.conversation_history)}"
                )

        self.agent_logger.log_max_iterations_reached()
        self.stats.mark_query_failed()
        return "Maximum iterations reached without finding an answer."

    async def _execute_pipeline_action(
        self, agent_response: AgentResponse, iteration: int, current_query: str
    ) -> Optional[Dict[str, Any]]:
        """Execute a pipeline action and handle timing."""
        action_start_time = time.time()

        pipeline_name = agent_response.action.name
        pipeline_input = {}

        try:
            await self._log_info(f"Executing pipeline: {pipeline_name}")
            await self._log_info(f"Pipeline reason: {agent_response.action.reason}")

            self.agent_logger.log_pipeline_start(pipeline_name, pipeline_input)
            self.stats.increment_pipeline_executions()

            # Execute pipeline by intent
            intent = agent_response.intent
            result = await self.pipeline_manager.execute_pipeline(
                intent, current_query, **pipeline_input
            )

            # Update timing
            action_time = time.time() - action_start_time
            self.stats.add_action_time(action_time)

            for output in self.state.llm_outputs:
                if output.iteration == iteration:
                    output.action_time_seconds = action_time
                    break

            await self._log_info(f"Pipeline result: {result}")
            self.agent_logger.log_pipeline_complete(pipeline_name, action_time)

            return result

        except Exception as e:
            action_time = time.time() - action_start_time
            self.agent_logger.log_pipeline_error(pipeline_name, e)

            # Update action time even on error
            for output in self.state.llm_outputs:
                if output.iteration == iteration:
                    output.action_time_seconds = action_time
                    break

            return {"error": str(e)}

    async def _log_info(self, message: str):
        """Log info."""
        if self.verbose:
            logger.info(message)

    async def _get_agent_response(
        self, query: str, iteration: int
    ) -> Optional[AgentResponse]:
        """Get and process agent response for an iteration."""
        self.agent_logger.log_thinking_start(iteration)

        prompt = load_prompt(
            "agent_react",
            query=query,
            history=self.state.format_history(),
            conversation_history=self.state.format_conversation_history(),
            pipelines=self.pipeline_manager.list_pipelines(),
        )

        thinking_start_time = time.time()

        # Use structured output instead of manual JSON parsing
        try:
            llm_response, metadata = await self.llm.generate_structured_output(
                prompt=prompt,
                response_model=AgentResponse,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
            )

            thinking_time = time.time() - thinking_start_time
            self.stats.add_thinking_time(thinking_time)
            self.agent_logger.log_thinking_complete(iteration, thinking_time)

            # Track LLM interaction - now llm_response is already an AgentResponse object
            llm_output = LLMOutput(
                iteration=iteration,
                prompt=prompt,
                response=llm_response.model_dump_json(),  # Convert to JSON string for logging
                intent=llm_response.intent,
                follow_up_question=llm_response.follow_up_question,
                parsed_response=llm_response,
                thinking_time_seconds=thinking_time,
                input_tokens=metadata.get("prompt_tokens", 0),
                output_tokens=metadata.get(
                    "completion_tokens", 0
                ),
            )

            self.agent_logger.log_agent_decision(
                action=(
                    llm_response.action.model_dump() if llm_response.action else None
                ),
                answer=llm_response.answer,
            )

            self.state.llm_outputs.append(llm_output)
            return llm_response

        except Exception as e:
            thinking_time = time.time() - thinking_start_time
            logger.error(f"Error in structured response generation: {e}")

            # Fallback: try regular generation and manual parsing
            try:
                fallback_response = await self.llm.generate(prompt)
                agent_response = AgentResponse.from_llm_response(fallback_response.text)

                llm_output = LLMOutput(
                    iteration=iteration,
                    prompt=prompt,
                    response=fallback_response.text,
                    intent=agent_response.intent,
                    follow_up_question=agent_response.follow_up_question,
                    parsed_response=agent_response,
                    thinking_time_seconds=thinking_time,
                    input_tokens=fallback_response.metadata.get("prompt_tokens", 0),
                    output_tokens=fallback_response.metadata.get(
                        "completion_tokens", 0
                    ),
                )

                self.agent_logger.log_agent_decision(
                    action=(
                        agent_response.action.model_dump()
                        if agent_response.action
                        else None
                    ),
                    answer=agent_response.answer,
                )

                self.state.llm_outputs.append(llm_output)
                return agent_response

            except Exception as fallback_error:
                llm_output = LLMOutput(
                    iteration=iteration,
                    prompt=prompt,
                    response=str(e),
                    intent="error",
                    follow_up_question=False,
                    parsed_response=None,
                    thinking_time_seconds=thinking_time,
                )
                self.agent_logger.log_parsing_error(iteration, fallback_error)
                self.state.llm_outputs.append(llm_output)
                return None

    async def _reformulate_query(self, user_input: str) -> str:
        """Reformulate the query based on conversation history and user input."""
        try:
            recent_history = self.state.get_recent_conversation_history()
            formatted_history = ""
            for entry in recent_history:
                formatted_history += f"User: {entry.user}\n"
                if entry.agent:
                    formatted_history += f"Agent: {entry.agent}\n"
                formatted_history += "\n"

            prompt = self.llm.load_prompt(
                "query_reformulation",
                original_query=recent_history[0].user if recent_history else user_input,
                conversation_history=formatted_history,
                user_input=user_input,
            )

            response = await self.llm.generate(prompt, temperature=0.1)
            reformulated_query = response.text.strip()

            return reformulated_query

        except Exception as e:
            self.agent_logger.log_query_reformulation_error(e)
            return user_input

    async def _rephrase_final_answer_for_conversation(self, current_answer: str) -> str:
        """Rephrase the final answer to maintain conversation flow."""
        try:
            if self.verbose:
                await self._log_info("Starting answer rephrasing for conversation flow")

            recent_history = self.state.get_recent_conversation_history()
            formatted_history = ""
            for entry in recent_history[:-1]:  # Exclude current query
                formatted_history += f"<User>\n{entry.user}\n</User>\n"
                if entry.agent:
                    formatted_history += f"<Agent>\n{entry.agent}\n</Agent>\n"
                formatted_history += "\n"

            if self.verbose:
                await self._log_info(
                    f"Formatted history for rephrasing: {len(formatted_history)} characters"
                )

            prompt = self.llm.load_prompt(
                "final_answer_generator",
                conversation_history=formatted_history,
                current_answer=current_answer,
                current_query=self.state.query,
                instructions="Rephrase the current answer to maintain conversational flow with the previous responses. Keep the same factual content and information, but adjust the tone and references to show this is a continuation of the conversation.",
            )

            if self.verbose:
                await self._log_info("Generating rephrased answer...")

            response = await self.llm.generate(prompt, temperature=0.1)
            rephrased_answer = response.text.strip()

            if self.verbose:
                await self._log_info(f"Original answer length: {len(current_answer)}")
                await self._log_info(
                    f"Rephrased answer length: {len(rephrased_answer)}"
                )
                await self._log_info("Answer rephrasing completed successfully")

            return rephrased_answer

        except Exception as e:
            await self._log_info(f"Error during rephrasing: {e}")
            self.agent_logger.log_final_answer_rephrase_error(e)
            return current_answer  # Fallback to original answer

    def get_execution_stats(self) -> Dict[str, Any]:
        """Get execution statistics including pipeline metrics."""
        base_stats = self.stats.get_stats()

        # Add pipeline metrics if available
        if hasattr(self.pipeline_manager, "tracker"):
            pipeline_metrics = self.pipeline_manager.get_metrics_summary()
            base_stats["pipeline_metrics"] = pipeline_metrics

        return base_stats

    @property
    def log_files(self) -> Dict[str, Any]:
        """Get log file paths."""
        return self.agent_logger.log_files

    async def cleanup(self):
        """Clean up any resources used by the agent."""
        self.agent_logger.log_cleanup_start()

        # Save final metrics
        if hasattr(self.pipeline_manager, "tracker"):
            self.pipeline_manager.tracker.save_metrics()
            logger.info(
                f"Final metrics saved: {self.pipeline_manager.tracker.get_summary()}"
            )

        if hasattr(self, "pipeline_manager") and self.pipeline_manager is not None:
            self.pipeline_manager.clear_all_caches()

        if self.owns_session and self.session is not None and not self.session.closed:
            try:
                self.agent_logger.log_session_close("agent")
                await self.session.close()
            except Exception as e:
                logger.error(f"Error closing agent session: {e}")
            self.session = None

        if hasattr(self, "client_session") and self.client_session is not None:
            self.agent_logger.log_session_close("client")
            await self.client_session.close()

        self.agent_logger.log_cleanup_complete()
